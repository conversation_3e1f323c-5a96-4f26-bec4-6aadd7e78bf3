// Placeholder image URLs for development
// Replace these with <PERSON><PERSON>'s actual images later

export const PLACEHOLDER_IMAGES = {
  // Profile images
  profile: {
    main: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    about: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=600&h=600&fit=crop&crop=face',
    hero: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop&crop=face',
  },
  
  // Project screenshots
  projects: {
    aiFramework: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=600&h=400&fit=crop',
    mlLibrary: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?w=600&h=400&fit=crop',
    dataViz: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop',
    webApp: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=400&fit=crop',
    mobileApp: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=600&h=400&fit=crop',
    researchTool: 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop',
    dashboard: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=600&h=400&fit=crop',
    api: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=600&h=400&fit=crop',
  },
  
  // Blog post images
  blog: {
    aiResearch: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=300&fit=crop',
    techTutorial: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=300&fit=crop',
    careerAdvice: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=300&fit=crop',
    research: 'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=600&h=300&fit=crop',
  },
  
  // Volunteering images
  volunteering: {
    teaching: 'https://images.unsplash.com/photo-1509062522246-3755977927d7?w=600&h=400&fit=crop',
    mentoring: 'https://images.unsplash.com/photo-1544717297-fa95b6ee9643?w=600&h=400&fit=crop',
    community: 'https://images.unsplash.com/photo-1559027615-cd4628902d4a?w=600&h=400&fit=crop',
    workshop: 'https://images.unsplash.com/photo-1517245386807-bb43f82c33c4?w=600&h=400&fit=crop',
  },
  
  // Background and decorative images
  backgrounds: {
    hero: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=1920&h=1080&fit=crop',
    about: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?w=1920&h=1080&fit=crop',
    research: 'https://images.unsplash.com/photo-1507146426996-ef05306b995a?w=1920&h=1080&fit=crop',
  },
  
  // Technology and tools
  tech: {
    python: 'https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=100&h=100&fit=crop',
    javascript: 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=100&h=100&fit=crop',
    react: 'https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=100&h=100&fit=crop',
    ai: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=100&h=100&fit=crop',
  }
};

// Helper function to get placeholder image with fallback
export function getPlaceholderImage(category: keyof typeof PLACEHOLDER_IMAGES, key: string, fallback?: string): string {
  const categoryImages = PLACEHOLDER_IMAGES[category] as Record<string, string>;
  return categoryImages[key] || fallback || PLACEHOLDER_IMAGES.profile.main;
}

// Generate placeholder for any size
export function generatePlaceholder(width: number, height: number, text?: string): string {
  const displayText = text || `${width}x${height}`;
  return `https://via.placeholder.com/${width}x${height}/3b82f6/ffffff?text=${encodeURIComponent(displayText)}`;
}
