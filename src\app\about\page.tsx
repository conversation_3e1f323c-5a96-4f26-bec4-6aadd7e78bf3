import { Metadata } from 'next';
import { AboutHero } from '@/components/sections/AboutHero';
import { Timeline } from '@/components/sections/Timeline';
import { Stats } from '@/components/sections/Stats';
import { Skills } from '@/components/sections/Skills';
import { ResumeSection } from '@/components/features/resume/ResumeDownload';
import personalData from '@/data/personal.json';
import timelineData from '@/data/timeline.json';
import statsData from '@/data/stats.json';
import skillsData from '@/data/skills.json';

export const metadata: Metadata = {
  title: 'About Me',
  description: 'Learn about my academic journey, professional experience, and passion for AI research and community engagement.',
  keywords: ['about', 'biography', 'education', 'experience', 'PhD student', 'computer science', 'AI research'],
};

export default function AboutPage() {
  return (
    <div className="space-y-0">
      {/* About Hero Section */}
      <AboutHero
        name={personalData.name}
        title={personalData.title}
        bio={personalData.bio}
        location={personalData.location}
        profileImage={personalData.profile_image}
        resumeUrl={personalData.resume_url}
      />

      {/* Stats Section */}
      <Stats stats={statsData} />

      {/* Skills Section */}
      <Skills skillCategories={skillsData} />

      {/* Timeline Section */}
      <Timeline
        events={timelineData}
        title="My Academic & Professional Journey"
        description="From early community involvement to cutting-edge research, here's how my passion for technology and education has shaped my career path."
      />

      {/* Resume Section */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Professional Resume</h2>
              <p className="text-lg text-muted-foreground">
                Download my complete professional resume highlighting my education at UESTC,
                software engineering experience, and research contributions.
              </p>
            </div>
            <div className="flex justify-center">
              <ResumeSection />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
