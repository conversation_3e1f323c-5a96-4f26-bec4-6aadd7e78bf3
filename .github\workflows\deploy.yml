name: 🚀 Deploy Portfolio

on:
  push:
    branches: [main, master]
  pull_request:
    branches: [main, master]

env:
  NODE_VERSION: '18'
  NEXT_PUBLIC_SITE_URL: https://premkatuwal.com
  NEXT_PUBLIC_SITE_NAME: "<PERSON><PERSON> Katuwal - AI Researcher & Software Engineer"

jobs:
  # Quality Assurance Job
  qa:
    name: 🔍 Quality Assurance
    runs-on: ubuntu-latest
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📋 Install dependencies
        run: npm ci
        
      - name: 🔧 Type checking
        run: npm run type-check
        
      - name: 🧹 Linting
        run: npm run lint
        
      - name: 🎨 Format checking
        run: npm run format:check
        
      - name: 🧪 Run tests
        run: npm run test:ci
        
      - name: 📊 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          
      - name: 🔒 Security audit
        run: npm audit --audit-level=high
        
      - name: 📈 Bundle analysis
        run: npm run analyze
        env:
          ANALYZE: true

  # Accessibility Testing Job
  accessibility:
    name: ♿ Accessibility Testing
    runs-on: ubuntu-latest
    needs: qa
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📋 Install dependencies
        run: npm ci
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🚀 Start application
        run: npm start &
        
      - name: ⏳ Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000
        
      - name: ♿ Run accessibility tests
        run: npm run test:accessibility
        
      - name: 🔍 Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouse.config.js'
          uploadArtifacts: true
          temporaryPublicStorage: true

  # Performance Testing Job
  performance:
    name: ⚡ Performance Testing
    runs-on: ubuntu-latest
    needs: qa
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📋 Install dependencies
        run: npm ci
        
      - name: ⚡ Run performance tests
        run: npm run test:performance
        
      - name: 📊 Performance budget check
        run: |
          npm run build
          npx bundlesize

  # Build and Deploy Job
  deploy:
    name: 🚀 Deploy to Production
    runs-on: ubuntu-latest
    needs: [qa, accessibility, performance]
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📋 Install dependencies
        run: npm ci
        
      - name: 🏗️ Build application
        run: npm run build
        env:
          NEXT_PUBLIC_SITE_URL: ${{ env.NEXT_PUBLIC_SITE_URL }}
          NEXT_PUBLIC_GA_MEASUREMENT_ID: ${{ secrets.GA_MEASUREMENT_ID }}
          NEXT_PUBLIC_VERCEL_ANALYTICS_ID: ${{ secrets.VERCEL_ANALYTICS_ID }}
          
      - name: 🚀 Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ./
          
      - name: 🔗 Get deployment URL
        id: deployment
        run: echo "url=$(vercel ls --token ${{ secrets.VERCEL_TOKEN }} | grep ${{ github.sha }} | awk '{print $2}')" >> $GITHUB_OUTPUT
        
      - name: 🧪 Post-deployment tests
        run: |
          sleep 30  # Wait for deployment to be ready
          curl -f ${{ steps.deployment.outputs.url }}/api/health || exit 1
          
      - name: 📊 Update deployment status
        run: |
          curl -X POST ${{ steps.deployment.outputs.url }}/api/deployment \
            -H "Content-Type: application/json" \
            -d '{"action":"deployment.succeeded","deployment_id":"${{ github.sha }}","status":"success"}'

  # Preview Deployment for PRs
  preview:
    name: 🔍 Preview Deployment
    runs-on: ubuntu-latest
    needs: [qa]
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        
      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: 📋 Install dependencies
        run: npm ci
        
      - name: 🏗️ Build application
        run: npm run build
        
      - name: 🔍 Deploy preview
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          github-comment: true
          working-directory: ./

  # Notification Job
  notify:
    name: 📢 Notifications
    runs-on: ubuntu-latest
    needs: [deploy]
    if: always()
    
    steps:
      - name: 📢 Deployment Success
        if: needs.deploy.result == 'success'
        run: |
          echo "🎉 Deployment successful!"
          echo "Portfolio is live at: ${{ env.NEXT_PUBLIC_SITE_URL }}"
          
      - name: 🚨 Deployment Failure
        if: needs.deploy.result == 'failure'
        run: |
          echo "❌ Deployment failed!"
          echo "Please check the logs and try again."
          
      - name: 📊 Deployment Summary
        run: |
          echo "## 📊 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ needs.deploy.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: Production" >> $GITHUB_STEP_SUMMARY
          echo "- **URL**: ${{ env.NEXT_PUBLIC_SITE_URL }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
