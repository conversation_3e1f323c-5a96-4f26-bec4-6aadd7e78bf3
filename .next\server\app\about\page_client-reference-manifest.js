globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/about/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/analytics/GoogleAnalytics.tsx":{"*":{"id":"(ssr)/./src/components/analytics/GoogleAnalytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/WebVitals.tsx":{"*":{"id":"(ssr)/./src/components/analytics/WebVitals.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(ssr)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/FeaturedWork.tsx":{"*":{"id":"(ssr)/./src/components/sections/FeaturedWork.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.tsx":{"*":{"id":"(ssr)/./src/components/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Stats.tsx":{"*":{"id":"(ssr)/./src/components/sections/Stats.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/features/resume/ResumeDownload.tsx":{"*":{"id":"(ssr)/./src/components/features/resume/ResumeDownload.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/AboutHero.tsx":{"*":{"id":"(ssr)/./src/components/sections/AboutHero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Skills.tsx":{"*":{"id":"(ssr)/./src/components/sections/Skills.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Timeline.tsx":{"*":{"id":"(ssr)/./src/components/sections/Timeline.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/research/page.tsx":{"*":{"id":"(ssr)/./src/app/research/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/journey/JourneyPageClient.tsx":{"*":{"id":"(ssr)/./src/app/journey/JourneyPageClient.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx":{"id":"(app-pages-browser)/./src/components/analytics/GoogleAnalytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\WebVitals.tsx":{"id":"(app-pages-browser)/./src/components/analytics/WebVitals.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Footer.tsx":{"id":"(app-pages-browser)/./src/components/layout/Footer.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\FeaturedWork.tsx":{"id":"(app-pages-browser)/./src/components/sections/FeaturedWork.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/sections/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Stats.tsx":{"id":"(app-pages-browser)/./src/components/sections/Stats.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\features\\resume\\ResumeDownload.tsx":{"id":"(app-pages-browser)/./src/components/features/resume/ResumeDownload.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\AboutHero.tsx":{"id":"(app-pages-browser)/./src/components/sections/AboutHero.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Skills.tsx":{"id":"(app-pages-browser)/./src/components/sections/Skills.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Timeline.tsx":{"id":"(app-pages-browser)/./src/components/sections/Timeline.tsx","name":"*","chunks":["app/about/page","static/chunks/app/about/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\research\\page.tsx":{"id":"(app-pages-browser)/./src/app/research/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\journey\\JourneyPageClient.tsx":{"id":"(app-pages-browser)/./src/app/journey/JourneyPageClient.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\pf\\src\\":[],"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\page":[],"C:\\Users\\<USER>\\Desktop\\pf\\src\\app\\about\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/GoogleAnalytics.tsx":{"*":{"id":"(rsc)/./src/components/analytics/GoogleAnalytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/WebVitals.tsx":{"*":{"id":"(rsc)/./src/components/analytics/WebVitals.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.tsx":{"*":{"id":"(rsc)/./src/components/layout/Footer.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/FeaturedWork.tsx":{"*":{"id":"(rsc)/./src/components/sections/FeaturedWork.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.tsx":{"*":{"id":"(rsc)/./src/components/sections/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Stats.tsx":{"*":{"id":"(rsc)/./src/components/sections/Stats.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/features/resume/ResumeDownload.tsx":{"*":{"id":"(rsc)/./src/components/features/resume/ResumeDownload.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/AboutHero.tsx":{"*":{"id":"(rsc)/./src/components/sections/AboutHero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Skills.tsx":{"*":{"id":"(rsc)/./src/components/sections/Skills.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Timeline.tsx":{"*":{"id":"(rsc)/./src/components/sections/Timeline.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/research/page.tsx":{"*":{"id":"(rsc)/./src/app/research/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/journey/JourneyPageClient.tsx":{"*":{"id":"(rsc)/./src/app/journey/JourneyPageClient.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}