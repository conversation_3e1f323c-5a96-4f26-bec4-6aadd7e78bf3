import { render, screen, fireEvent, waitFor } from '@/__tests__/utils/test-utils'
import { ContactForm } from '@/components/features/contact/ContactForm'

// Mock fetch for form submissions
global.fetch = jest.fn()

describe('Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Contact Form Integration', () => {
    it('should handle complete form submission flow', async () => {
      // Mock successful API response
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      })

      render(<ContactForm />)

      // Fill out the form
      const nameInput = screen.getByLabelText(/name/i)
      const emailInput = screen.getByLabelText(/email/i)
      const subjectInput = screen.getByLabelText(/subject/i)
      const messageInput = screen.getByLabelText(/message/i)
      const submitButton = screen.getByRole('button', { name: /send message/i })

      fireEvent.change(nameInput, { target: { value: 'Prem Katuwal' } })
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(subjectInput, { target: { value: 'Test Subject' } })
      fireEvent.change(messageInput, { target: { value: 'This is a test message with enough content.' } })

      // Submit the form
      fireEvent.click(submitButton)

      // Check loading state
      expect(screen.getByText(/sending message/i)).toBeInTheDocument()

      // Wait for success message
      await waitFor(() => {
        expect(screen.getByText(/thank you! your message has been sent successfully/i)).toBeInTheDocument()
      })

      // Verify form was reset
      expect(nameInput).toHaveValue('')
      expect(emailInput).toHaveValue('')
      expect(subjectInput).toHaveValue('')
      expect(messageInput).toHaveValue('')
    })

    it('should handle form validation errors', async () => {
      render(<ContactForm />)

      const submitButton = screen.getByRole('button', { name: /send message/i })

      // Try to submit empty form
      fireEvent.click(submitButton)

      // Check for validation errors
      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument()
        expect(screen.getByText(/email is required/i)).toBeInTheDocument()
        expect(screen.getByText(/subject is required/i)).toBeInTheDocument()
        expect(screen.getByText(/message is required/i)).toBeInTheDocument()
      })
    })

    it('should handle API errors gracefully', async () => {
      // Mock API error
      ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('API Error'))

      render(<ContactForm />)

      // Fill out the form with valid data
      const nameInput = screen.getByLabelText(/name/i)
      const emailInput = screen.getByLabelText(/email/i)
      const subjectInput = screen.getByLabelText(/subject/i)
      const messageInput = screen.getByLabelText(/message/i)
      const submitButton = screen.getByRole('button', { name: /send message/i })

      fireEvent.change(nameInput, { target: { value: 'Prem Katuwal' } })
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
      fireEvent.change(subjectInput, { target: { value: 'Test Subject' } })
      fireEvent.change(messageInput, { target: { value: 'This is a test message with enough content.' } })

      // Submit the form
      fireEvent.click(submitButton)

      // Wait for error message
      await waitFor(() => {
        expect(screen.getByText(/sorry, there was an error sending your message/i)).toBeInTheDocument()
      })
    })

    it('should validate email format', async () => {
      render(<ContactForm />)

      const emailInput = screen.getByLabelText(/email/i)
      const submitButton = screen.getByRole('button', { name: /send message/i })

      // Enter invalid email
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/please enter a valid email address/i)).toBeInTheDocument()
      })
    })

    it('should validate minimum field lengths', async () => {
      render(<ContactForm />)

      const nameInput = screen.getByLabelText(/name/i)
      const subjectInput = screen.getByLabelText(/subject/i)
      const messageInput = screen.getByLabelText(/message/i)
      const submitButton = screen.getByRole('button', { name: /send message/i })

      // Enter values that are too short
      fireEvent.change(nameInput, { target: { value: 'A' } })
      fireEvent.change(subjectInput, { target: { value: 'Hi' } })
      fireEvent.change(messageInput, { target: { value: 'Short' } })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/name must be at least 2 characters/i)).toBeInTheDocument()
        expect(screen.getByText(/subject must be at least 5 characters/i)).toBeInTheDocument()
        expect(screen.getByText(/message must be at least 10 characters/i)).toBeInTheDocument()
      })
    })

    it('should clear validation errors when user starts typing', async () => {
      render(<ContactForm />)

      const nameInput = screen.getByLabelText(/name/i)
      const submitButton = screen.getByRole('button', { name: /send message/i })

      // Trigger validation error
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      })

      // Start typing to clear error
      fireEvent.change(nameInput, { target: { value: 'P' } })

      await waitFor(() => {
        expect(screen.queryByText(/name is required/i)).not.toBeInTheDocument()
      })
    })
  })

  describe('Navigation Integration', () => {
    it('should handle navigation between pages', () => {
      // This would test navigation if we had a full page setup
      // For now, we'll test that navigation components render correctly
      
      const navigationItems = [
        { name: 'Home', href: '/' },
        { name: 'About', href: '/about' },
        { name: 'Projects', href: '/projects' },
        { name: 'Contact', href: '/contact' },
      ]

      render(
        <nav>
          {navigationItems.map((item) => (
            <a key={item.name} href={item.href}>
              {item.name}
            </a>
          ))}
        </nav>
      )

      navigationItems.forEach((item) => {
        const link = screen.getByRole('link', { name: item.name })
        expect(link).toHaveAttribute('href', item.href)
      })
    })
  })

  describe('Search and Filter Integration', () => {
    it('should handle search functionality', async () => {
      const mockProjects = [
        { id: '1', title: 'React Project', description: 'A React application', technologies: ['React'] },
        { id: '2', title: 'Vue Project', description: 'A Vue application', technologies: ['Vue'] },
        { id: '3', title: 'Angular Project', description: 'An Angular application', technologies: ['Angular'] },
      ]

      const SearchableList = () => {
        const [searchTerm, setSearchTerm] = React.useState('')
        
        const filteredProjects = mockProjects.filter(project =>
          project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          project.description.toLowerCase().includes(searchTerm.toLowerCase())
        )

        return (
          <div>
            <input
              type="text"
              placeholder="Search projects..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Search projects"
            />
            <div>
              {filteredProjects.map(project => (
                <div key={project.id} data-testid={`project-${project.id}`}>
                  {project.title}
                </div>
              ))}
            </div>
          </div>
        )
      }

      render(<SearchableList />)

      const searchInput = screen.getByLabelText(/search projects/i)

      // Initially all projects should be visible
      expect(screen.getByTestId('project-1')).toBeInTheDocument()
      expect(screen.getByTestId('project-2')).toBeInTheDocument()
      expect(screen.getByTestId('project-3')).toBeInTheDocument()

      // Search for React
      fireEvent.change(searchInput, { target: { value: 'React' } })

      await waitFor(() => {
        expect(screen.getByTestId('project-1')).toBeInTheDocument()
        expect(screen.queryByTestId('project-2')).not.toBeInTheDocument()
        expect(screen.queryByTestId('project-3')).not.toBeInTheDocument()
      })

      // Clear search
      fireEvent.change(searchInput, { target: { value: '' } })

      await waitFor(() => {
        expect(screen.getByTestId('project-1')).toBeInTheDocument()
        expect(screen.getByTestId('project-2')).toBeInTheDocument()
        expect(screen.getByTestId('project-3')).toBeInTheDocument()
      })
    })
  })

  describe('Responsive Behavior', () => {
    it('should handle viewport changes', () => {
      // Mock window.matchMedia for responsive testing
      const mockMatchMedia = (query: string) => ({
        matches: query.includes('768px'), // Simulate mobile viewport
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })

      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: mockMatchMedia,
      })

      const ResponsiveComponent = () => {
        const [isMobile, setIsMobile] = React.useState(false)

        React.useEffect(() => {
          const mediaQuery = window.matchMedia('(max-width: 768px)')
          setIsMobile(mediaQuery.matches)
        }, [])

        return (
          <div>
            {isMobile ? 'Mobile View' : 'Desktop View'}
          </div>
        )
      }

      render(<ResponsiveComponent />)

      // Should show mobile view based on our mock
      expect(screen.getByText('Mobile View')).toBeInTheDocument()
    })
  })
})

// Add React import for JSX
import React from 'react'
