import { NextRequest, NextResponse } from 'next/server'

// Health check endpoint for monitoring
export async function GET(request: NextRequest) {
  const startTime = Date.now()
  
  try {
    // Basic health checks
    const checks = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      checks: {
        database: await checkDatabase(),
        external_apis: await checkExternalAPIs(),
        file_system: await checkFileSystem(),
        environment_variables: checkEnvironmentVariables(),
      }
    }

    const responseTime = Date.now() - startTime
    
    // Determine overall health status
    const allChecksHealthy = Object.values(checks.checks).every(check => check.status === 'healthy')
    const overallStatus = allChecksHealthy ? 'healthy' : 'degraded'

    return NextResponse.json({
      ...checks,
      status: overallStatus,
      response_time_ms: responseTime,
    }, {
      status: overallStatus === 'healthy' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    })
  } catch (error) {
    const responseTime = Date.now() - startTime
    
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      error: error instanceof Error ? error.message : 'Unknown error',
      response_time_ms: responseTime,
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    })
  }
}

// Database health check (placeholder for future database integration)
async function checkDatabase() {
  try {
    // For now, just check if DATABASE_URL is configured
    const databaseUrl = process.env.DATABASE_URL
    
    if (!databaseUrl) {
      return {
        status: 'healthy',
        message: 'No database configured (static site)',
        response_time_ms: 0,
      }
    }

    // In the future, add actual database connectivity check
    return {
      status: 'healthy',
      message: 'Database connection successful',
      response_time_ms: 5,
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'Database check failed',
      response_time_ms: 0,
    }
  }
}

// External APIs health check
async function checkExternalAPIs() {
  const checks = []
  
  try {
    // Check Google Analytics availability
    if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {
      const gaCheck = await checkGoogleAnalytics()
      checks.push({ service: 'Google Analytics', ...gaCheck })
    }

    // Check if any external API calls are working
    const externalApiCheck = await checkExternalServices()
    checks.push({ service: 'External Services', ...externalApiCheck })

    const allHealthy = checks.every(check => check.status === 'healthy')
    
    return {
      status: allHealthy ? 'healthy' : 'degraded',
      services: checks,
      total_services: checks.length,
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'External API check failed',
      services: checks,
    }
  }
}

// Google Analytics health check
async function checkGoogleAnalytics() {
  try {
    // Simple check to see if GA is configured
    const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID
    
    if (!gaId) {
      return {
        status: 'degraded',
        message: 'Google Analytics not configured',
        response_time_ms: 0,
      }
    }

    return {
      status: 'healthy',
      message: 'Google Analytics configured',
      measurement_id: gaId.substring(0, 5) + '***',
      response_time_ms: 1,
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'Google Analytics check failed',
      response_time_ms: 0,
    }
  }
}

// External services check
async function checkExternalServices() {
  try {
    const startTime = Date.now()
    
    // Check if we can make external requests (test with a simple API)
    const response = await fetch('https://api.github.com/zen', {
      method: 'GET',
      headers: { 'User-Agent': 'Portfolio-Health-Check' },
      signal: AbortSignal.timeout(5000), // 5 second timeout
    })
    
    const responseTime = Date.now() - startTime
    
    if (response.ok) {
      return {
        status: 'healthy',
        message: 'External API connectivity working',
        response_time_ms: responseTime,
      }
    } else {
      return {
        status: 'degraded',
        message: `External API returned ${response.status}`,
        response_time_ms: responseTime,
      }
    }
  } catch (error) {
    return {
      status: 'degraded',
      message: 'External API connectivity issues',
      response_time_ms: 0,
    }
  }
}

// File system health check
async function checkFileSystem() {
  try {
    const fs = await import('fs/promises')
    const path = await import('path')
    
    // Check if we can read critical files
    const criticalFiles = [
      'package.json',
      'next.config.js',
      'tailwind.config.ts',
    ]
    
    const fileChecks = await Promise.all(
      criticalFiles.map(async (file) => {
        try {
          await fs.access(path.join(process.cwd(), file))
          return { file, status: 'exists' }
        } catch {
          return { file, status: 'missing' }
        }
      })
    )
    
    const allFilesExist = fileChecks.every(check => check.status === 'exists')
    
    return {
      status: allFilesExist ? 'healthy' : 'degraded',
      message: allFilesExist ? 'All critical files accessible' : 'Some critical files missing',
      files: fileChecks,
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      message: 'File system check failed',
      error: error instanceof Error ? error.message : 'Unknown error',
    }
  }
}

// Environment variables check
function checkEnvironmentVariables() {
  const requiredEnvVars = [
    'NODE_ENV',
    'NEXT_PUBLIC_SITE_URL',
  ]
  
  const optionalEnvVars = [
    'NEXT_PUBLIC_GA_MEASUREMENT_ID',
    'CONTACT_EMAIL',
    'SMTP_HOST',
  ]
  
  const envChecks = {
    required: requiredEnvVars.map(envVar => ({
      variable: envVar,
      status: process.env[envVar] ? 'set' : 'missing',
      value: process.env[envVar] ? '***' : undefined,
    })),
    optional: optionalEnvVars.map(envVar => ({
      variable: envVar,
      status: process.env[envVar] ? 'set' : 'not_set',
      value: process.env[envVar] ? '***' : undefined,
    })),
  }
  
  const allRequiredSet = envChecks.required.every(check => check.status === 'set')
  
  return {
    status: allRequiredSet ? 'healthy' : 'degraded',
    message: allRequiredSet ? 'All required environment variables set' : 'Some required environment variables missing',
    variables: envChecks,
  }
}
