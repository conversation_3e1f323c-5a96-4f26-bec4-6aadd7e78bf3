'use client';

import { forwardRef } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const typographyVariants = cva('', {
  variants: {
    variant: {
      h1: 'scroll-m-20 text-4xl font-bold tracking-tight lg:text-6xl xl:text-7xl leading-tight',
      h2: 'scroll-m-20 border-b pb-3 text-3xl font-semibold tracking-tight first:mt-0 lg:text-4xl leading-tight',
      h3: 'scroll-m-20 text-2xl font-semibold tracking-tight lg:text-3xl leading-tight',
      h4: 'scroll-m-20 text-xl font-semibold tracking-tight lg:text-2xl leading-tight',
      h5: 'scroll-m-20 text-lg font-semibold tracking-tight lg:text-xl leading-tight',
      h6: 'scroll-m-20 text-base font-semibold tracking-tight lg:text-lg leading-tight',
      p: 'leading-relaxed text-base lg:text-lg [&:not(:first-child)]:mt-6',
      lead: 'text-xl lg:text-2xl text-muted-foreground leading-relaxed',
      large: 'text-lg lg:text-xl font-semibold leading-relaxed',
      small: 'text-sm font-medium leading-relaxed',
      muted: 'text-sm lg:text-base text-muted-foreground leading-relaxed',
      code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',
    },
  },
  defaultVariants: {
    variant: 'p',
  },
});

export interface TypographyProps
  extends React.HTMLAttributes<HTMLElement>,
    VariantProps<typeof typographyVariants> {
  as?: keyof JSX.IntrinsicElements;
}

const Typography = forwardRef<HTMLElement, TypographyProps>(
  ({ className, variant, as, ...props }, ref) => {
    const Comp = as || getDefaultElement(variant);
    
    return (
      <Comp
        className={cn(typographyVariants({ variant, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);

Typography.displayName = 'Typography';

function getDefaultElement(variant: TypographyProps['variant']) {
  switch (variant) {
    case 'h1':
      return 'h1';
    case 'h2':
      return 'h2';
    case 'h3':
      return 'h3';
    case 'h4':
      return 'h4';
    case 'h5':
      return 'h5';
    case 'h6':
      return 'h6';
    case 'lead':
    case 'large':
    case 'small':
    case 'muted':
    case 'p':
      return 'p';
    case 'code':
      return 'code';
    default:
      return 'p';
  }
}

export { Typography, typographyVariants };
