module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/about',
        'http://localhost:3000/projects',
        'http://localhost:3000/research',
        'http://localhost:3000/blog',
        'http://localhost:3000/contact',
        'http://localhost:3000/journey',
        'http://localhost:3000/testimonials',
      ],
      startServerCommand: 'npm start',
      startServerReadyPattern: 'ready on',
      startServerReadyTimeout: 30000,
      numberOfRuns: 3,
      settings: {
        chromeFlags: '--no-sandbox --disable-dev-shm-usage',
        preset: 'desktop',
        throttling: {
          rttMs: 40,
          throughputKbps: 10240,
          cpuSlowdownMultiplier: 1,
          requestLatencyMs: 0,
          downloadThroughputKbps: 0,
          uploadThroughputKbps: 0,
        },
        screenEmulation: {
          mobile: false,
          width: 1350,
          height: 940,
          deviceScaleFactor: 1,
          disabled: false,
        },
        formFactor: 'desktop',
        onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
      },
    },
    assert: {
      assertions: {
        // Performance thresholds
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.95 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.95 }],
        
        // Core Web Vitals
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 2500 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['error', { maxNumericValue: 300 }],
        
        // Performance metrics
        'speed-index': ['error', { maxNumericValue: 3000 }],
        'interactive': ['error', { maxNumericValue: 3500 }],
        
        // Accessibility
        'color-contrast': 'error',
        'image-alt': 'error',
        'label': 'error',
        'link-name': 'error',
        'button-name': 'error',
        
        // SEO
        'document-title': 'error',
        'meta-description': 'error',
        'http-status-code': 'error',
        'crawlable-anchors': 'error',
        
        // Best Practices
        'uses-https': 'error',
        'is-on-https': 'error',
        'uses-http2': 'warn',
        'no-vulnerable-libraries': 'error',
        
        // Progressive Web App (if applicable)
        'installable-manifest': 'warn',
        'splash-screen': 'warn',
        'themed-omnibox': 'warn',
        
        // Resource optimization
        'unused-css-rules': ['warn', { maxLength: 2000 }],
        'unused-javascript': ['warn', { maxLength: 20000 }],
        'modern-image-formats': 'warn',
        'uses-optimized-images': 'warn',
        'uses-text-compression': 'error',
        'uses-responsive-images': 'warn',
        
        // Caching
        'uses-long-term-cache': 'warn',
        'efficient-animated-content': 'warn',
        
        // JavaScript
        'no-unload-listeners': 'error',
        'uses-passive-event-listeners': 'warn',
        
        // Network
        'render-blocking-resources': ['warn', { maxLength: 0 }],
        'redirects': 'error',
        'uses-rel-preconnect': 'warn',
        'uses-rel-preload': 'warn',
      },
      preset: 'lighthouse:recommended',
    },
    upload: {
      target: 'temporary-public-storage',
      reportFilenamePattern: '%%PATHNAME%%-%%DATETIME%%-report.%%EXTENSION%%',
    },
    server: {
      port: 9001,
      storage: './lighthouse-reports',
    },
  },
  
  // Custom audit configuration
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      // Performance
      'first-contentful-paint',
      'largest-contentful-paint',
      'cumulative-layout-shift',
      'total-blocking-time',
      'speed-index',
      'interactive',
      
      // Accessibility
      'color-contrast',
      'image-alt',
      'label',
      'link-name',
      'button-name',
      'heading-order',
      'landmark-one-main',
      'list',
      'listitem',
      'meta-viewport',
      
      // SEO
      'document-title',
      'meta-description',
      'http-status-code',
      'crawlable-anchors',
      'is-crawlable',
      'robots-txt',
      'hreflang',
      'canonical',
      
      // Best Practices
      'uses-https',
      'is-on-https',
      'no-vulnerable-libraries',
      'charset',
      'doctype',
      'no-document-write',
      'external-anchors-use-rel-noopener',
      
      // Performance optimizations
      'unused-css-rules',
      'unused-javascript',
      'modern-image-formats',
      'uses-optimized-images',
      'uses-text-compression',
      'uses-responsive-images',
      'efficient-animated-content',
      'uses-long-term-cache',
      'render-blocking-resources',
      'uses-rel-preconnect',
      'uses-rel-preload',
      'critical-request-chains',
      'user-timings',
      'bootup-time',
      'mainthread-work-breakdown',
      'font-display',
      'third-party-summary',
      'largest-contentful-paint-element',
      'layout-shift-elements',
      'uses-passive-event-listeners',
      'no-unload-listeners',
      'non-composited-animations',
      'unsized-images',
      'preload-lcp-image',
      'total-byte-weight',
      'offscreen-images',
      'unminified-css',
      'unminified-javascript',
      'legacy-javascript',
    ],
  },
  
  // Budget configuration for performance monitoring
  budgets: [
    {
      path: '/*',
      timings: [
        {
          metric: 'first-contentful-paint',
          budget: 2000,
        },
        {
          metric: 'largest-contentful-paint',
          budget: 2500,
        },
        {
          metric: 'cumulative-layout-shift',
          budget: 0.1,
        },
        {
          metric: 'total-blocking-time',
          budget: 300,
        },
        {
          metric: 'speed-index',
          budget: 3000,
        },
        {
          metric: 'interactive',
          budget: 3500,
        },
      ],
      resourceSizes: [
        {
          resourceType: 'script',
          budget: 300,
        },
        {
          resourceType: 'stylesheet',
          budget: 100,
        },
        {
          resourceType: 'image',
          budget: 500,
        },
        {
          resourceType: 'font',
          budget: 100,
        },
        {
          resourceType: 'total',
          budget: 1000,
        },
      ],
      resourceCounts: [
        {
          resourceType: 'script',
          budget: 10,
        },
        {
          resourceType: 'stylesheet',
          budget: 5,
        },
        {
          resourceType: 'image',
          budget: 20,
        },
        {
          resourceType: 'font',
          budget: 3,
        },
        {
          resourceType: 'total',
          budget: 50,
        },
      ],
    },
  ],
}
