/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module2, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/GoogleAnalytics.tsx */ \"(rsc)/./src/components/analytics/GoogleAnalytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/WebVitals.tsx */ \"(rsc)/./src/components/analytics/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"10655f5eba39\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByZW1rXFxEZXNrdG9wXFxwZlxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMTA2NTVmNWViYTM5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/seo/StructuredData */ \"(rsc)/./src/components/seo/StructuredData.tsx\");\n/* harmony import */ var _components_analytics_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/analytics/GoogleAnalytics */ \"(rsc)/./src/components/analytics/GoogleAnalytics.tsx\");\n/* harmony import */ var _components_analytics_WebVitals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/analytics/WebVitals */ \"(rsc)/./src/components/analytics/WebVitals.tsx\");\n/* harmony import */ var _components_scripts_ThemeScript__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/scripts/ThemeScript */ \"(rsc)/./src/components/scripts/ThemeScript.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n/* harmony import */ var _lib_seo__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/seo */ \"(rsc)/./src/lib/seo.ts\");\n\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.name,\n        template: `%s | ${_lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.description,\n    keywords: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.keywords,\n    authors: [\n        {\n            name: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.author\n        }\n    ],\n    creator: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.author,\n    metadataBase: new URL(_lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.url),\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.url,\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.description,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.name\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_8__.SITE_CONFIG.description,\n        creator: \"@yourtwitter\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_scripts_ThemeScript__WEBPACK_IMPORTED_MODULE_7__.ThemeScript, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__.StructuredData, {\n                        data: (0,_lib_seo__WEBPACK_IMPORTED_MODULE_9__.generatePersonStructuredData)()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__.StructuredData, {\n                        data: (0,_lib_seo__WEBPACK_IMPORTED_MODULE_9__.generateWebsiteStructuredData)()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_10___default().className)} font-sans antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_5__.GoogleAnalytics, {\n                        measurementId: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_WebVitals__WEBPACK_IMPORTED_MODULE_6__.WebVitals, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/analytics/GoogleAnalytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/analytics/GoogleAnalytics.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),
/* harmony export */   trackDownload: () => (/* binding */ trackDownload),
/* harmony export */   trackEvent: () => (/* binding */ trackEvent),
/* harmony export */   trackExternalLink: () => (/* binding */ trackExternalLink),
/* harmony export */   trackFormSubmission: () => (/* binding */ trackFormSubmission),
/* harmony export */   trackPageView: () => (/* binding */ trackPageView)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const GoogleAnalytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"GoogleAnalytics",
);const trackEvent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackEvent() from the server but trackEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackEvent",
);const trackPageView = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackPageView() from the server but trackPageView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackPageView",
);const trackDownload = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackDownload() from the server but trackDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackDownload",
);const trackExternalLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackExternalLink() from the server but trackExternalLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackExternalLink",
);const trackFormSubmission = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackFormSubmission() from the server but trackFormSubmission is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackFormSubmission",
);

/***/ }),

/***/ "(rsc)/./src/components/analytics/WebVitals.tsx":
/*!************************************************!*\
  !*** ./src/components/analytics/WebVitals.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WebVitals: () => (/* binding */ WebVitals)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\WebVitals.tsx",
"WebVitals",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/scripts/ThemeScript.tsx":
/*!************************************************!*\
  !*** ./src/components/scripts/ThemeScript.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeScript: () => (/* binding */ ThemeScript)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ThemeScript() {\n    const themeScript = `\n    (function() {\n      try {\n        var theme = localStorage.getItem('theme');\n        var systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n        var resolvedTheme = theme === 'system' || !theme ? systemTheme : theme;\n        \n        if (resolvedTheme === 'dark') {\n          document.documentElement.classList.add('dark');\n          document.documentElement.style.colorScheme = 'dark';\n        } else {\n          document.documentElement.classList.add('light');\n          document.documentElement.style.colorScheme = 'light';\n        }\n      } catch (e) {\n        // Fallback to light theme if there's an error\n        document.documentElement.classList.add('light');\n        document.documentElement.style.colorScheme = 'light';\n      }\n    })();\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: themeScript\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\scripts\\\\ThemeScript.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9zY3JpcHRzL1RoZW1lU2NyaXB0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQU8sU0FBU0E7SUFDZCxNQUFNQyxjQUFjLENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0VBb0JyQixDQUFDO0lBRUQscUJBQ0UsOERBQUNDO1FBQ0NDLHlCQUF5QjtZQUN2QkMsUUFBUUg7UUFDVjs7Ozs7O0FBR04iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJlbWtcXERlc2t0b3BcXHBmXFxzcmNcXGNvbXBvbmVudHNcXHNjcmlwdHNcXFRoZW1lU2NyaXB0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gVGhlbWVTY3JpcHQoKSB7XG4gIGNvbnN0IHRoZW1lU2NyaXB0ID0gYFxuICAgIChmdW5jdGlvbigpIHtcbiAgICAgIHRyeSB7XG4gICAgICAgIHZhciB0aGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0aGVtZScpO1xuICAgICAgICB2YXIgc3lzdGVtVGhlbWUgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpLm1hdGNoZXMgPyAnZGFyaycgOiAnbGlnaHQnO1xuICAgICAgICB2YXIgcmVzb2x2ZWRUaGVtZSA9IHRoZW1lID09PSAnc3lzdGVtJyB8fCAhdGhlbWUgPyBzeXN0ZW1UaGVtZSA6IHRoZW1lO1xuICAgICAgICBcbiAgICAgICAgaWYgKHJlc29sdmVkVGhlbWUgPT09ICdkYXJrJykge1xuICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QuYWRkKCdkYXJrJyk7XG4gICAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLmNvbG9yU2NoZW1lID0gJ2RhcmsnO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5jbGFzc0xpc3QuYWRkKCdsaWdodCcpO1xuICAgICAgICAgIGRvY3VtZW50LmRvY3VtZW50RWxlbWVudC5zdHlsZS5jb2xvclNjaGVtZSA9ICdsaWdodCc7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgLy8gRmFsbGJhY2sgdG8gbGlnaHQgdGhlbWUgaWYgdGhlcmUncyBhbiBlcnJvclxuICAgICAgICBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnQuY2xhc3NMaXN0LmFkZCgnbGlnaHQnKTtcbiAgICAgICAgZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50LnN0eWxlLmNvbG9yU2NoZW1lID0gJ2xpZ2h0JztcbiAgICAgIH1cbiAgICB9KSgpO1xuICBgO1xuXG4gIHJldHVybiAoXG4gICAgPHNjcmlwdFxuICAgICAgZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3tcbiAgICAgICAgX19odG1sOiB0aGVtZVNjcmlwdCxcbiAgICAgIH19XG4gICAgLz5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJUaGVtZVNjcmlwdCIsInRoZW1lU2NyaXB0Iiwic2NyaXB0IiwiZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUwiLCJfX2h0bWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/scripts/ThemeScript.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/seo/StructuredData.tsx":
/*!***********************************************!*\
  !*** ./src/components/seo/StructuredData.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StructuredData: () => (/* binding */ StructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction StructuredData({ data }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(data, null, 2)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9zZW8vU3RydWN0dXJlZERhdGEudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFJTyxTQUFTQSxlQUFlLEVBQUVDLElBQUksRUFBdUI7SUFDMUQscUJBQ0UsOERBQUNDO1FBQ0NDLE1BQUs7UUFDTEMseUJBQXlCO1lBQ3ZCQyxRQUFRQyxLQUFLQyxTQUFTLENBQUNOLE1BQU0sTUFBTTtRQUNyQzs7Ozs7O0FBR04iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJlbWtcXERlc2t0b3BcXHBmXFxzcmNcXGNvbXBvbmVudHNcXHNlb1xcU3RydWN0dXJlZERhdGEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBTdHJ1Y3R1cmVkRGF0YVByb3BzIHtcbiAgZGF0YTogUmVjb3JkPHN0cmluZywgYW55PiB8IEFycmF5PFJlY29yZDxzdHJpbmcsIGFueT4+O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gU3RydWN0dXJlZERhdGEoeyBkYXRhIH06IFN0cnVjdHVyZWREYXRhUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8c2NyaXB0XG4gICAgICB0eXBlPVwiYXBwbGljYXRpb24vbGQranNvblwiXG4gICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpLFxuICAgICAgfX1cbiAgICAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlN0cnVjdHVyZWREYXRhIiwiZGF0YSIsInNjcmlwdCIsInR5cGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_VARIANTS: () => (/* binding */ ANIMATION_VARIANTS),\n/* harmony export */   BLOG_CATEGORIES: () => (/* binding */ BLOG_CATEGORIES),\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   PROJECT_CATEGORIES: () => (/* binding */ PROJECT_CATEGORIES),\n/* harmony export */   PUBLICATION_TYPES: () => (/* binding */ PUBLICATION_TYPES),\n/* harmony export */   SITE_CONFIG: () => (/* binding */ SITE_CONFIG),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_COLORS: () => (/* binding */ THEME_COLORS),\n/* harmony export */   TIMELINE_TYPES: () => (/* binding */ TIMELINE_TYPES)\n/* harmony export */ });\n// Application constants\nconst SITE_CONFIG = {\n    name: 'Prem Katuwal - Portfolio',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n    author: 'Prem Katuwal',\n    keywords: [\n        'Computer Science',\n        'Master\\'s Student',\n        'AI Research',\n        'UESTC',\n        'Machine Learning',\n        'Software Development',\n        'Publications',\n        'Portfolio',\n        'Artificial Intelligence',\n        'Chengdu',\n        'China'\n    ]\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About',\n        href: '/about'\n    },\n    {\n        name: 'Journey',\n        href: '/journey'\n    },\n    {\n        name: 'Research',\n        href: '/research'\n    },\n    {\n        name: 'Projects',\n        href: '/projects'\n    },\n    {\n        name: 'Volunteering',\n        href: '/volunteering'\n    },\n    {\n        name: 'Blog',\n        href: '/blog'\n    },\n    {\n        name: 'Testimonials',\n        href: '/testimonials'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nconst SOCIAL_LINKS = {\n    github: 'https://github.com',\n    linkedin: 'https://linkedin.com/in',\n    twitter: 'https://twitter.com',\n    scholar: 'https://scholar.google.com/citations?user=',\n    orcid: 'https://orcid.org/'\n};\nconst PROJECT_CATEGORIES = {\n    'ai-ml': 'AI & Machine Learning',\n    'web-dev': 'Web Development',\n    'research-tools': 'Research Tools',\n    'open-source': 'Open Source'\n};\nconst PUBLICATION_TYPES = {\n    journal: 'Journal Article',\n    conference: 'Conference Paper',\n    preprint: 'Preprint',\n    thesis: 'Thesis'\n};\nconst BLOG_CATEGORIES = {\n    research: 'Research',\n    technology: 'Technology',\n    career: 'Career',\n    tutorials: 'Tutorials'\n};\nconst TIMELINE_TYPES = {\n    education: 'Education',\n    work: 'Work Experience',\n    research: 'Research',\n    volunteering: 'Volunteering'\n};\nconst CONTACT_INFO = {\n    email: '<EMAIL>',\n    location: 'Chengdu, China',\n    availability: 'Available for research collaborations and opportunities'\n};\nconst ANIMATION_VARIANTS = {\n    fadeIn: {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1\n        }\n    },\n    slideUp: {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideDown: {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideLeft: {\n        hidden: {\n            opacity: 0,\n            x: 20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    slideRight: {\n        hidden: {\n            opacity: 0,\n            x: -20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    scale: {\n        hidden: {\n            opacity: 0,\n            scale: 0.95\n        },\n        visible: {\n            opacity: 1,\n            scale: 1\n        }\n    }\n};\nconst BREAKPOINTS = {\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    '2xl': 1536\n};\nconst THEME_COLORS = {\n    primary: {\n        50: '#eff6ff',\n        100: '#dbeafe',\n        500: '#3b82f6',\n        600: '#2563eb',\n        700: '#1d4ed8',\n        900: '#1e3a8a'\n    },\n    secondary: {\n        50: '#f8fafc',\n        100: '#f1f5f9',\n        500: '#64748b',\n        600: '#475569',\n        700: '#334155',\n        900: '#0f172a'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seo.ts":
/*!************************!*\
  !*** ./src/lib/seo.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateBlogPostStructuredData: () => (/* binding */ generateBlogPostStructuredData),\n/* harmony export */   generateBreadcrumbStructuredData: () => (/* binding */ generateBreadcrumbStructuredData),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generatePersonStructuredData: () => (/* binding */ generatePersonStructuredData),\n/* harmony export */   generateResearchArticleStructuredData: () => (/* binding */ generateResearchArticleStructuredData),\n/* harmony export */   generateWebsiteStructuredData: () => (/* binding */ generateWebsiteStructuredData),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\n// Base SEO configuration\nconst siteConfig = {\n    name: 'Prem Katuwal - AI Researcher & Software Engineer',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC (ranked 3rd globally in AI), with 2 years of professional software engineering experience. Passionate about bridging academia and industry through innovative AI research and community impact.',\n    url: 'https://premkatuwal.com',\n    ogImage: 'https://premkatuwal.com/og-image.jpg',\n    author: {\n        name: 'Prem Katuwal',\n        email: '<EMAIL>',\n        twitter: '@premkatuwal',\n        linkedin: 'https://linkedin.com/in/premkatuwal',\n        github: 'https://github.com/Katwal-77'\n    },\n    keywords: [\n        'AI Research',\n        'Machine Learning',\n        'Software Engineering',\n        'PhD Student',\n        'Computer Science',\n        'Research Publications',\n        'Open Source',\n        'Community Volunteering',\n        'Technology Education',\n        'Academic Research',\n        'Industry Experience',\n        'Full Stack Development',\n        'Data Science',\n        'Artificial Intelligence',\n        'Software Development'\n    ]\n};\n// Generate metadata for pages\nfunction generateMetadata({ title, description, image, url, type = 'website', publishedTime, modifiedTime, keywords = [] }) {\n    const metaTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;\n    const metaDescription = description || siteConfig.description;\n    const metaImage = image || siteConfig.ogImage;\n    const metaUrl = url ? `${siteConfig.url}${url}` : siteConfig.url;\n    const allKeywords = [\n        ...siteConfig.keywords,\n        ...keywords\n    ];\n    return {\n        title: metaTitle,\n        description: metaDescription,\n        keywords: allKeywords.join(', '),\n        authors: [\n            {\n                name: siteConfig.author.name,\n                url: siteConfig.url\n            }\n        ],\n        creator: siteConfig.author.name,\n        publisher: siteConfig.author.name,\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        openGraph: {\n            type,\n            locale: 'en_US',\n            url: metaUrl,\n            title: metaTitle,\n            description: metaDescription,\n            siteName: siteConfig.name,\n            images: [\n                {\n                    url: metaImage,\n                    width: 1200,\n                    height: 630,\n                    alt: metaTitle\n                }\n            ],\n            ...type === 'article' && {\n                publishedTime,\n                modifiedTime,\n                authors: [\n                    siteConfig.author.name\n                ]\n            }\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: metaTitle,\n            description: metaDescription,\n            images: [\n                metaImage\n            ],\n            creator: siteConfig.author.twitter,\n            site: siteConfig.author.twitter\n        },\n        alternates: {\n            canonical: metaUrl\n        },\n        other: {\n            'google-site-verification': 'your-google-verification-code'\n        }\n    };\n}\n// Structured data generators\nfunction generatePersonStructuredData() {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'Person',\n        name: siteConfig.author.name,\n        url: siteConfig.url,\n        image: siteConfig.ogImage,\n        sameAs: [\n            siteConfig.author.linkedin,\n            siteConfig.author.github,\n            `https://twitter.com/${siteConfig.author.twitter.replace('@', '')}`\n        ],\n        jobTitle: 'Master\\'s Student & AI Researcher',\n        worksFor: {\n            '@type': 'Organization',\n            name: 'University of Electronic Science and Technology of China (UESTC)'\n        },\n        alumniOf: {\n            '@type': 'Organization',\n            name: 'University of Electronic Science and Technology of China'\n        },\n        knowsAbout: [\n            'Artificial Intelligence',\n            'Machine Learning',\n            'Software Engineering',\n            'Computer Science Research',\n            'Data Science',\n            'Full Stack Development'\n        ],\n        email: siteConfig.author.email,\n        description: siteConfig.description\n    };\n}\nfunction generateWebsiteStructuredData() {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name: siteConfig.name,\n        url: siteConfig.url,\n        description: siteConfig.description,\n        author: {\n            '@type': 'Person',\n            name: siteConfig.author.name\n        },\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n                '@type': 'EntryPoint',\n                urlTemplate: `${siteConfig.url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n        }\n    };\n}\nfunction generateBlogPostStructuredData({ title, description, url, image, publishedAt, updatedAt, readingTime, tags }) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'BlogPosting',\n        headline: title,\n        description,\n        url: `${siteConfig.url}${url}`,\n        image: image || siteConfig.ogImage,\n        datePublished: publishedAt,\n        dateModified: updatedAt || publishedAt,\n        author: {\n            '@type': 'Person',\n            name: siteConfig.author.name,\n            url: siteConfig.url\n        },\n        publisher: {\n            '@type': 'Person',\n            name: siteConfig.author.name,\n            url: siteConfig.url\n        },\n        keywords: tags.join(', '),\n        wordCount: readingTime * 200,\n        timeRequired: `PT${readingTime}M`,\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${siteConfig.url}${url}`\n        }\n    };\n}\nfunction generateResearchArticleStructuredData({ title, abstract, authors, publishedAt, journal, doi, url }) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'ScholarlyArticle',\n        headline: title,\n        abstract,\n        url: `${siteConfig.url}${url}`,\n        datePublished: publishedAt,\n        author: authors.map((author)=>({\n                '@type': 'Person',\n                name: author\n            })),\n        publisher: journal ? {\n            '@type': 'Organization',\n            name: journal\n        } : undefined,\n        identifier: doi ? {\n            '@type': 'PropertyValue',\n            propertyID: 'DOI',\n            value: doi\n        } : undefined,\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${siteConfig.url}${url}`\n        }\n    };\n}\nfunction generateBreadcrumbStructuredData(items) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: items.map((item, index)=>({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.name,\n                item: `${siteConfig.url}${item.url}`\n            }))\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Nlby50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRUEseUJBQXlCO0FBQ2xCLE1BQU1BLGFBQWE7SUFDeEJDLE1BQU07SUFDTkMsYUFBYTtJQUNiQyxLQUFLO0lBQ0xDLFNBQVM7SUFDVEMsUUFBUTtRQUNOSixNQUFNO1FBQ05LLE9BQU87UUFDUEMsU0FBUztRQUNUQyxVQUFVO1FBQ1ZDLFFBQVE7SUFDVjtJQUNBQyxVQUFVO1FBQ1I7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSCxFQUFFO0FBRUYsOEJBQThCO0FBQ3ZCLFNBQVNDLGlCQUFpQixFQUMvQkMsS0FBSyxFQUNMVixXQUFXLEVBQ1hXLEtBQUssRUFDTFYsR0FBRyxFQUNIVyxPQUFPLFNBQVMsRUFDaEJDLGFBQWEsRUFDYkMsWUFBWSxFQUNaTixXQUFXLEVBQUUsRUFVZDtJQUNDLE1BQU1PLFlBQVlMLFFBQVEsR0FBR0EsTUFBTSxHQUFHLEVBQUVaLFdBQVdDLElBQUksRUFBRSxHQUFHRCxXQUFXQyxJQUFJO0lBQzNFLE1BQU1pQixrQkFBa0JoQixlQUFlRixXQUFXRSxXQUFXO0lBQzdELE1BQU1pQixZQUFZTixTQUFTYixXQUFXSSxPQUFPO0lBQzdDLE1BQU1nQixVQUFVakIsTUFBTSxHQUFHSCxXQUFXRyxHQUFHLEdBQUdBLEtBQUssR0FBR0gsV0FBV0csR0FBRztJQUNoRSxNQUFNa0IsY0FBYztXQUFJckIsV0FBV1UsUUFBUTtXQUFLQTtLQUFTO0lBRXpELE9BQU87UUFDTEUsT0FBT0s7UUFDUGYsYUFBYWdCO1FBQ2JSLFVBQVVXLFlBQVlDLElBQUksQ0FBQztRQUMzQkMsU0FBUztZQUFDO2dCQUFFdEIsTUFBTUQsV0FBV0ssTUFBTSxDQUFDSixJQUFJO2dCQUFFRSxLQUFLSCxXQUFXRyxHQUFHO1lBQUM7U0FBRTtRQUNoRXFCLFNBQVN4QixXQUFXSyxNQUFNLENBQUNKLElBQUk7UUFDL0J3QixXQUFXekIsV0FBV0ssTUFBTSxDQUFDSixJQUFJO1FBQ2pDeUIsUUFBUTtZQUNOQyxPQUFPO1lBQ1BDLFFBQVE7WUFDUkMsV0FBVztnQkFDVEYsT0FBTztnQkFDUEMsUUFBUTtnQkFDUixxQkFBcUIsQ0FBQztnQkFDdEIscUJBQXFCO2dCQUNyQixlQUFlLENBQUM7WUFDbEI7UUFDRjtRQUNBRSxXQUFXO1lBQ1RoQjtZQUNBaUIsUUFBUTtZQUNSNUIsS0FBS2lCO1lBQ0xSLE9BQU9LO1lBQ1BmLGFBQWFnQjtZQUNiYyxVQUFVaEMsV0FBV0MsSUFBSTtZQUN6QmdDLFFBQVE7Z0JBQ047b0JBQ0U5QixLQUFLZ0I7b0JBQ0xlLE9BQU87b0JBQ1BDLFFBQVE7b0JBQ1JDLEtBQUtuQjtnQkFDUDthQUNEO1lBQ0QsR0FBSUgsU0FBUyxhQUFhO2dCQUN4QkM7Z0JBQ0FDO2dCQUNBTyxTQUFTO29CQUFDdkIsV0FBV0ssTUFBTSxDQUFDSixJQUFJO2lCQUFDO1lBQ25DLENBQUM7UUFDSDtRQUNBTSxTQUFTO1lBQ1A4QixNQUFNO1lBQ056QixPQUFPSztZQUNQZixhQUFhZ0I7WUFDYmUsUUFBUTtnQkFBQ2Q7YUFBVTtZQUNuQkssU0FBU3hCLFdBQVdLLE1BQU0sQ0FBQ0UsT0FBTztZQUNsQytCLE1BQU10QyxXQUFXSyxNQUFNLENBQUNFLE9BQU87UUFDakM7UUFDQWdDLFlBQVk7WUFDVkMsV0FBV3BCO1FBQ2I7UUFDQXFCLE9BQU87WUFDTCw0QkFBNEI7UUFDOUI7SUFDRjtBQUNGO0FBRUEsNkJBQTZCO0FBQ3RCLFNBQVNDO0lBQ2QsT0FBTztRQUNMLFlBQVk7UUFDWixTQUFTO1FBQ1R6QyxNQUFNRCxXQUFXSyxNQUFNLENBQUNKLElBQUk7UUFDNUJFLEtBQUtILFdBQVdHLEdBQUc7UUFDbkJVLE9BQU9iLFdBQVdJLE9BQU87UUFDekJ1QyxRQUFRO1lBQ04zQyxXQUFXSyxNQUFNLENBQUNHLFFBQVE7WUFDMUJSLFdBQVdLLE1BQU0sQ0FBQ0ksTUFBTTtZQUN4QixDQUFDLG9CQUFvQixFQUFFVCxXQUFXSyxNQUFNLENBQUNFLE9BQU8sQ0FBQ3FDLE9BQU8sQ0FBQyxLQUFLLEtBQUs7U0FDcEU7UUFDREMsVUFBVTtRQUNWQyxVQUFVO1lBQ1IsU0FBUztZQUNUN0MsTUFBTTtRQUNSO1FBQ0E4QyxVQUFVO1lBQ1IsU0FBUztZQUNUOUMsTUFBTTtRQUNSO1FBQ0ErQyxZQUFZO1lBQ1Y7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFDRDFDLE9BQU9OLFdBQVdLLE1BQU0sQ0FBQ0MsS0FBSztRQUM5QkosYUFBYUYsV0FBV0UsV0FBVztJQUNyQztBQUNGO0FBRU8sU0FBUytDO0lBQ2QsT0FBTztRQUNMLFlBQVk7UUFDWixTQUFTO1FBQ1RoRCxNQUFNRCxXQUFXQyxJQUFJO1FBQ3JCRSxLQUFLSCxXQUFXRyxHQUFHO1FBQ25CRCxhQUFhRixXQUFXRSxXQUFXO1FBQ25DRyxRQUFRO1lBQ04sU0FBUztZQUNUSixNQUFNRCxXQUFXSyxNQUFNLENBQUNKLElBQUk7UUFDOUI7UUFDQWlELGlCQUFpQjtZQUNmLFNBQVM7WUFDVEMsUUFBUTtnQkFDTixTQUFTO2dCQUNUQyxhQUFhLEdBQUdwRCxXQUFXRyxHQUFHLENBQUMsOEJBQThCLENBQUM7WUFDaEU7WUFDQSxlQUFlO1FBQ2pCO0lBQ0Y7QUFDRjtBQUVPLFNBQVNrRCwrQkFBK0IsRUFDN0N6QyxLQUFLLEVBQ0xWLFdBQVcsRUFDWEMsR0FBRyxFQUNIVSxLQUFLLEVBQ0x5QyxXQUFXLEVBQ1hDLFNBQVMsRUFDVEMsV0FBVyxFQUNYQyxJQUFJLEVBVUw7SUFDQyxPQUFPO1FBQ0wsWUFBWTtRQUNaLFNBQVM7UUFDVEMsVUFBVTlDO1FBQ1ZWO1FBQ0FDLEtBQUssR0FBR0gsV0FBV0csR0FBRyxHQUFHQSxLQUFLO1FBQzlCVSxPQUFPQSxTQUFTYixXQUFXSSxPQUFPO1FBQ2xDdUQsZUFBZUw7UUFDZk0sY0FBY0wsYUFBYUQ7UUFDM0JqRCxRQUFRO1lBQ04sU0FBUztZQUNUSixNQUFNRCxXQUFXSyxNQUFNLENBQUNKLElBQUk7WUFDNUJFLEtBQUtILFdBQVdHLEdBQUc7UUFDckI7UUFDQXNCLFdBQVc7WUFDVCxTQUFTO1lBQ1R4QixNQUFNRCxXQUFXSyxNQUFNLENBQUNKLElBQUk7WUFDNUJFLEtBQUtILFdBQVdHLEdBQUc7UUFDckI7UUFDQU8sVUFBVStDLEtBQUtuQyxJQUFJLENBQUM7UUFDcEJ1QyxXQUFXTCxjQUFjO1FBQ3pCTSxjQUFjLENBQUMsRUFBRSxFQUFFTixZQUFZLENBQUMsQ0FBQztRQUNqQ08sa0JBQWtCO1lBQ2hCLFNBQVM7WUFDVCxPQUFPLEdBQUcvRCxXQUFXRyxHQUFHLEdBQUdBLEtBQUs7UUFDbEM7SUFDRjtBQUNGO0FBRU8sU0FBUzZELHNDQUFzQyxFQUNwRHBELEtBQUssRUFDTHFELFFBQVEsRUFDUjFDLE9BQU8sRUFDUCtCLFdBQVcsRUFDWFksT0FBTyxFQUNQQyxHQUFHLEVBQ0hoRSxHQUFHLEVBU0o7SUFDQyxPQUFPO1FBQ0wsWUFBWTtRQUNaLFNBQVM7UUFDVHVELFVBQVU5QztRQUNWcUQ7UUFDQTlELEtBQUssR0FBR0gsV0FBV0csR0FBRyxHQUFHQSxLQUFLO1FBQzlCd0QsZUFBZUw7UUFDZmpELFFBQVFrQixRQUFRNkMsR0FBRyxDQUFDL0QsQ0FBQUEsU0FBVztnQkFDN0IsU0FBUztnQkFDVEosTUFBTUk7WUFDUjtRQUNBb0IsV0FBV3lDLFVBQVU7WUFDbkIsU0FBUztZQUNUakUsTUFBTWlFO1FBQ1IsSUFBSUc7UUFDSkMsWUFBWUgsTUFBTTtZQUNoQixTQUFTO1lBQ1RJLFlBQVk7WUFDWkMsT0FBT0w7UUFDVCxJQUFJRTtRQUNKTixrQkFBa0I7WUFDaEIsU0FBUztZQUNULE9BQU8sR0FBRy9ELFdBQVdHLEdBQUcsR0FBR0EsS0FBSztRQUNsQztJQUNGO0FBQ0Y7QUFFTyxTQUFTc0UsaUNBQWlDQyxLQUEyQztJQUMxRixPQUFPO1FBQ0wsWUFBWTtRQUNaLFNBQVM7UUFDVEMsaUJBQWlCRCxNQUFNTixHQUFHLENBQUMsQ0FBQ1EsTUFBTWpELFFBQVc7Z0JBQzNDLFNBQVM7Z0JBQ1RrRCxVQUFVbEQsUUFBUTtnQkFDbEIxQixNQUFNMkUsS0FBSzNFLElBQUk7Z0JBQ2YyRSxNQUFNLEdBQUc1RSxXQUFXRyxHQUFHLEdBQUd5RSxLQUFLekUsR0FBRyxFQUFFO1lBQ3RDO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwcmVta1xcRGVza3RvcFxccGZcXHNyY1xcbGliXFxzZW8udHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnO1xuXG4vLyBCYXNlIFNFTyBjb25maWd1cmF0aW9uXG5leHBvcnQgY29uc3Qgc2l0ZUNvbmZpZyA9IHtcbiAgbmFtZTogJ1ByZW0gS2F0dXdhbCAtIEFJIFJlc2VhcmNoZXIgJiBTb2Z0d2FyZSBFbmdpbmVlcicsXG4gIGRlc2NyaXB0aW9uOiAnTWFzdGVyXFwncyBzdHVkZW50IGluIENvbXB1dGVyIFNjaWVuY2UgKEFJKSBhdCBVRVNUQyAocmFua2VkIDNyZCBnbG9iYWxseSBpbiBBSSksIHdpdGggMiB5ZWFycyBvZiBwcm9mZXNzaW9uYWwgc29mdHdhcmUgZW5naW5lZXJpbmcgZXhwZXJpZW5jZS4gUGFzc2lvbmF0ZSBhYm91dCBicmlkZ2luZyBhY2FkZW1pYSBhbmQgaW5kdXN0cnkgdGhyb3VnaCBpbm5vdmF0aXZlIEFJIHJlc2VhcmNoIGFuZCBjb21tdW5pdHkgaW1wYWN0LicsXG4gIHVybDogJ2h0dHBzOi8vcHJlbWthdHV3YWwuY29tJyxcbiAgb2dJbWFnZTogJ2h0dHBzOi8vcHJlbWthdHV3YWwuY29tL29nLWltYWdlLmpwZycsXG4gIGF1dGhvcjoge1xuICAgIG5hbWU6ICdQcmVtIEthdHV3YWwnLFxuICAgIGVtYWlsOiAncHJlbWthdHdhbDk4N0BnbWFpbC5jb20nLFxuICAgIHR3aXR0ZXI6ICdAcHJlbWthdHV3YWwnLFxuICAgIGxpbmtlZGluOiAnaHR0cHM6Ly9saW5rZWRpbi5jb20vaW4vcHJlbWthdHV3YWwnLFxuICAgIGdpdGh1YjogJ2h0dHBzOi8vZ2l0aHViLmNvbS9LYXR3YWwtNzcnLFxuICB9LFxuICBrZXl3b3JkczogW1xuICAgICdBSSBSZXNlYXJjaCcsXG4gICAgJ01hY2hpbmUgTGVhcm5pbmcnLFxuICAgICdTb2Z0d2FyZSBFbmdpbmVlcmluZycsXG4gICAgJ1BoRCBTdHVkZW50JyxcbiAgICAnQ29tcHV0ZXIgU2NpZW5jZScsXG4gICAgJ1Jlc2VhcmNoIFB1YmxpY2F0aW9ucycsXG4gICAgJ09wZW4gU291cmNlJyxcbiAgICAnQ29tbXVuaXR5IFZvbHVudGVlcmluZycsXG4gICAgJ1RlY2hub2xvZ3kgRWR1Y2F0aW9uJyxcbiAgICAnQWNhZGVtaWMgUmVzZWFyY2gnLFxuICAgICdJbmR1c3RyeSBFeHBlcmllbmNlJyxcbiAgICAnRnVsbCBTdGFjayBEZXZlbG9wbWVudCcsXG4gICAgJ0RhdGEgU2NpZW5jZScsXG4gICAgJ0FydGlmaWNpYWwgSW50ZWxsaWdlbmNlJyxcbiAgICAnU29mdHdhcmUgRGV2ZWxvcG1lbnQnLFxuICBdLFxufTtcblxuLy8gR2VuZXJhdGUgbWV0YWRhdGEgZm9yIHBhZ2VzXG5leHBvcnQgZnVuY3Rpb24gZ2VuZXJhdGVNZXRhZGF0YSh7XG4gIHRpdGxlLFxuICBkZXNjcmlwdGlvbixcbiAgaW1hZ2UsXG4gIHVybCxcbiAgdHlwZSA9ICd3ZWJzaXRlJyxcbiAgcHVibGlzaGVkVGltZSxcbiAgbW9kaWZpZWRUaW1lLFxuICBrZXl3b3JkcyA9IFtdLFxufToge1xuICB0aXRsZT86IHN0cmluZztcbiAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gIGltYWdlPzogc3RyaW5nO1xuICB1cmw/OiBzdHJpbmc7XG4gIHR5cGU/OiAnd2Vic2l0ZScgfCAnYXJ0aWNsZSc7XG4gIHB1Ymxpc2hlZFRpbWU/OiBzdHJpbmc7XG4gIG1vZGlmaWVkVGltZT86IHN0cmluZztcbiAga2V5d29yZHM/OiBzdHJpbmdbXTtcbn0pOiBNZXRhZGF0YSB7XG4gIGNvbnN0IG1ldGFUaXRsZSA9IHRpdGxlID8gYCR7dGl0bGV9IHwgJHtzaXRlQ29uZmlnLm5hbWV9YCA6IHNpdGVDb25maWcubmFtZTtcbiAgY29uc3QgbWV0YURlc2NyaXB0aW9uID0gZGVzY3JpcHRpb24gfHwgc2l0ZUNvbmZpZy5kZXNjcmlwdGlvbjtcbiAgY29uc3QgbWV0YUltYWdlID0gaW1hZ2UgfHwgc2l0ZUNvbmZpZy5vZ0ltYWdlO1xuICBjb25zdCBtZXRhVXJsID0gdXJsID8gYCR7c2l0ZUNvbmZpZy51cmx9JHt1cmx9YCA6IHNpdGVDb25maWcudXJsO1xuICBjb25zdCBhbGxLZXl3b3JkcyA9IFsuLi5zaXRlQ29uZmlnLmtleXdvcmRzLCAuLi5rZXl3b3Jkc107XG5cbiAgcmV0dXJuIHtcbiAgICB0aXRsZTogbWV0YVRpdGxlLFxuICAgIGRlc2NyaXB0aW9uOiBtZXRhRGVzY3JpcHRpb24sXG4gICAga2V5d29yZHM6IGFsbEtleXdvcmRzLmpvaW4oJywgJyksXG4gICAgYXV0aG9yczogW3sgbmFtZTogc2l0ZUNvbmZpZy5hdXRob3IubmFtZSwgdXJsOiBzaXRlQ29uZmlnLnVybCB9XSxcbiAgICBjcmVhdG9yOiBzaXRlQ29uZmlnLmF1dGhvci5uYW1lLFxuICAgIHB1Ymxpc2hlcjogc2l0ZUNvbmZpZy5hdXRob3IubmFtZSxcbiAgICByb2JvdHM6IHtcbiAgICAgIGluZGV4OiB0cnVlLFxuICAgICAgZm9sbG93OiB0cnVlLFxuICAgICAgZ29vZ2xlQm90OiB7XG4gICAgICAgIGluZGV4OiB0cnVlLFxuICAgICAgICBmb2xsb3c6IHRydWUsXG4gICAgICAgICdtYXgtdmlkZW8tcHJldmlldyc6IC0xLFxuICAgICAgICAnbWF4LWltYWdlLXByZXZpZXcnOiAnbGFyZ2UnLFxuICAgICAgICAnbWF4LXNuaXBwZXQnOiAtMSxcbiAgICAgIH0sXG4gICAgfSxcbiAgICBvcGVuR3JhcGg6IHtcbiAgICAgIHR5cGUsXG4gICAgICBsb2NhbGU6ICdlbl9VUycsXG4gICAgICB1cmw6IG1ldGFVcmwsXG4gICAgICB0aXRsZTogbWV0YVRpdGxlLFxuICAgICAgZGVzY3JpcHRpb246IG1ldGFEZXNjcmlwdGlvbixcbiAgICAgIHNpdGVOYW1lOiBzaXRlQ29uZmlnLm5hbWUsXG4gICAgICBpbWFnZXM6IFtcbiAgICAgICAge1xuICAgICAgICAgIHVybDogbWV0YUltYWdlLFxuICAgICAgICAgIHdpZHRoOiAxMjAwLFxuICAgICAgICAgIGhlaWdodDogNjMwLFxuICAgICAgICAgIGFsdDogbWV0YVRpdGxlLFxuICAgICAgICB9LFxuICAgICAgXSxcbiAgICAgIC4uLih0eXBlID09PSAnYXJ0aWNsZScgJiYge1xuICAgICAgICBwdWJsaXNoZWRUaW1lLFxuICAgICAgICBtb2RpZmllZFRpbWUsXG4gICAgICAgIGF1dGhvcnM6IFtzaXRlQ29uZmlnLmF1dGhvci5uYW1lXSxcbiAgICAgIH0pLFxuICAgIH0sXG4gICAgdHdpdHRlcjoge1xuICAgICAgY2FyZDogJ3N1bW1hcnlfbGFyZ2VfaW1hZ2UnLFxuICAgICAgdGl0bGU6IG1ldGFUaXRsZSxcbiAgICAgIGRlc2NyaXB0aW9uOiBtZXRhRGVzY3JpcHRpb24sXG4gICAgICBpbWFnZXM6IFttZXRhSW1hZ2VdLFxuICAgICAgY3JlYXRvcjogc2l0ZUNvbmZpZy5hdXRob3IudHdpdHRlcixcbiAgICAgIHNpdGU6IHNpdGVDb25maWcuYXV0aG9yLnR3aXR0ZXIsXG4gICAgfSxcbiAgICBhbHRlcm5hdGVzOiB7XG4gICAgICBjYW5vbmljYWw6IG1ldGFVcmwsXG4gICAgfSxcbiAgICBvdGhlcjoge1xuICAgICAgJ2dvb2dsZS1zaXRlLXZlcmlmaWNhdGlvbic6ICd5b3VyLWdvb2dsZS12ZXJpZmljYXRpb24tY29kZScsXG4gICAgfSxcbiAgfTtcbn1cblxuLy8gU3RydWN0dXJlZCBkYXRhIGdlbmVyYXRvcnNcbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVBlcnNvblN0cnVjdHVyZWREYXRhKCkge1xuICByZXR1cm4ge1xuICAgICdAY29udGV4dCc6ICdodHRwczovL3NjaGVtYS5vcmcnLFxuICAgICdAdHlwZSc6ICdQZXJzb24nLFxuICAgIG5hbWU6IHNpdGVDb25maWcuYXV0aG9yLm5hbWUsXG4gICAgdXJsOiBzaXRlQ29uZmlnLnVybCxcbiAgICBpbWFnZTogc2l0ZUNvbmZpZy5vZ0ltYWdlLFxuICAgIHNhbWVBczogW1xuICAgICAgc2l0ZUNvbmZpZy5hdXRob3IubGlua2VkaW4sXG4gICAgICBzaXRlQ29uZmlnLmF1dGhvci5naXRodWIsXG4gICAgICBgaHR0cHM6Ly90d2l0dGVyLmNvbS8ke3NpdGVDb25maWcuYXV0aG9yLnR3aXR0ZXIucmVwbGFjZSgnQCcsICcnKX1gLFxuICAgIF0sXG4gICAgam9iVGl0bGU6ICdNYXN0ZXJcXCdzIFN0dWRlbnQgJiBBSSBSZXNlYXJjaGVyJyxcbiAgICB3b3Jrc0Zvcjoge1xuICAgICAgJ0B0eXBlJzogJ09yZ2FuaXphdGlvbicsXG4gICAgICBuYW1lOiAnVW5pdmVyc2l0eSBvZiBFbGVjdHJvbmljIFNjaWVuY2UgYW5kIFRlY2hub2xvZ3kgb2YgQ2hpbmEgKFVFU1RDKScsXG4gICAgfSxcbiAgICBhbHVtbmlPZjoge1xuICAgICAgJ0B0eXBlJzogJ09yZ2FuaXphdGlvbicsXG4gICAgICBuYW1lOiAnVW5pdmVyc2l0eSBvZiBFbGVjdHJvbmljIFNjaWVuY2UgYW5kIFRlY2hub2xvZ3kgb2YgQ2hpbmEnLFxuICAgIH0sXG4gICAga25vd3NBYm91dDogW1xuICAgICAgJ0FydGlmaWNpYWwgSW50ZWxsaWdlbmNlJyxcbiAgICAgICdNYWNoaW5lIExlYXJuaW5nJyxcbiAgICAgICdTb2Z0d2FyZSBFbmdpbmVlcmluZycsXG4gICAgICAnQ29tcHV0ZXIgU2NpZW5jZSBSZXNlYXJjaCcsXG4gICAgICAnRGF0YSBTY2llbmNlJyxcbiAgICAgICdGdWxsIFN0YWNrIERldmVsb3BtZW50JyxcbiAgICBdLFxuICAgIGVtYWlsOiBzaXRlQ29uZmlnLmF1dGhvci5lbWFpbCxcbiAgICBkZXNjcmlwdGlvbjogc2l0ZUNvbmZpZy5kZXNjcmlwdGlvbixcbiAgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlV2Vic2l0ZVN0cnVjdHVyZWREYXRhKCkge1xuICByZXR1cm4ge1xuICAgICdAY29udGV4dCc6ICdodHRwczovL3NjaGVtYS5vcmcnLFxuICAgICdAdHlwZSc6ICdXZWJTaXRlJyxcbiAgICBuYW1lOiBzaXRlQ29uZmlnLm5hbWUsXG4gICAgdXJsOiBzaXRlQ29uZmlnLnVybCxcbiAgICBkZXNjcmlwdGlvbjogc2l0ZUNvbmZpZy5kZXNjcmlwdGlvbixcbiAgICBhdXRob3I6IHtcbiAgICAgICdAdHlwZSc6ICdQZXJzb24nLFxuICAgICAgbmFtZTogc2l0ZUNvbmZpZy5hdXRob3IubmFtZSxcbiAgICB9LFxuICAgIHBvdGVudGlhbEFjdGlvbjoge1xuICAgICAgJ0B0eXBlJzogJ1NlYXJjaEFjdGlvbicsXG4gICAgICB0YXJnZXQ6IHtcbiAgICAgICAgJ0B0eXBlJzogJ0VudHJ5UG9pbnQnLFxuICAgICAgICB1cmxUZW1wbGF0ZTogYCR7c2l0ZUNvbmZpZy51cmx9L3NlYXJjaD9xPXtzZWFyY2hfdGVybV9zdHJpbmd9YCxcbiAgICAgIH0sXG4gICAgICAncXVlcnktaW5wdXQnOiAncmVxdWlyZWQgbmFtZT1zZWFyY2hfdGVybV9zdHJpbmcnLFxuICAgIH0sXG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZUJsb2dQb3N0U3RydWN0dXJlZERhdGEoe1xuICB0aXRsZSxcbiAgZGVzY3JpcHRpb24sXG4gIHVybCxcbiAgaW1hZ2UsXG4gIHB1Ymxpc2hlZEF0LFxuICB1cGRhdGVkQXQsXG4gIHJlYWRpbmdUaW1lLFxuICB0YWdzLFxufToge1xuICB0aXRsZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICB1cmw6IHN0cmluZztcbiAgaW1hZ2U/OiBzdHJpbmc7XG4gIHB1Ymxpc2hlZEF0OiBzdHJpbmc7XG4gIHVwZGF0ZWRBdD86IHN0cmluZztcbiAgcmVhZGluZ1RpbWU6IG51bWJlcjtcbiAgdGFnczogc3RyaW5nW107XG59KSB7XG4gIHJldHVybiB7XG4gICAgJ0Bjb250ZXh0JzogJ2h0dHBzOi8vc2NoZW1hLm9yZycsXG4gICAgJ0B0eXBlJzogJ0Jsb2dQb3N0aW5nJyxcbiAgICBoZWFkbGluZTogdGl0bGUsXG4gICAgZGVzY3JpcHRpb24sXG4gICAgdXJsOiBgJHtzaXRlQ29uZmlnLnVybH0ke3VybH1gLFxuICAgIGltYWdlOiBpbWFnZSB8fCBzaXRlQ29uZmlnLm9nSW1hZ2UsXG4gICAgZGF0ZVB1Ymxpc2hlZDogcHVibGlzaGVkQXQsXG4gICAgZGF0ZU1vZGlmaWVkOiB1cGRhdGVkQXQgfHwgcHVibGlzaGVkQXQsXG4gICAgYXV0aG9yOiB7XG4gICAgICAnQHR5cGUnOiAnUGVyc29uJyxcbiAgICAgIG5hbWU6IHNpdGVDb25maWcuYXV0aG9yLm5hbWUsXG4gICAgICB1cmw6IHNpdGVDb25maWcudXJsLFxuICAgIH0sXG4gICAgcHVibGlzaGVyOiB7XG4gICAgICAnQHR5cGUnOiAnUGVyc29uJyxcbiAgICAgIG5hbWU6IHNpdGVDb25maWcuYXV0aG9yLm5hbWUsXG4gICAgICB1cmw6IHNpdGVDb25maWcudXJsLFxuICAgIH0sXG4gICAga2V5d29yZHM6IHRhZ3Muam9pbignLCAnKSxcbiAgICB3b3JkQ291bnQ6IHJlYWRpbmdUaW1lICogMjAwLCAvLyBBcHByb3hpbWF0ZSB3b3JkIGNvdW50XG4gICAgdGltZVJlcXVpcmVkOiBgUFQke3JlYWRpbmdUaW1lfU1gLFxuICAgIG1haW5FbnRpdHlPZlBhZ2U6IHtcbiAgICAgICdAdHlwZSc6ICdXZWJQYWdlJyxcbiAgICAgICdAaWQnOiBgJHtzaXRlQ29uZmlnLnVybH0ke3VybH1gLFxuICAgIH0sXG4gIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZW5lcmF0ZVJlc2VhcmNoQXJ0aWNsZVN0cnVjdHVyZWREYXRhKHtcbiAgdGl0bGUsXG4gIGFic3RyYWN0LFxuICBhdXRob3JzLFxuICBwdWJsaXNoZWRBdCxcbiAgam91cm5hbCxcbiAgZG9pLFxuICB1cmwsXG59OiB7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGFic3RyYWN0OiBzdHJpbmc7XG4gIGF1dGhvcnM6IHN0cmluZ1tdO1xuICBwdWJsaXNoZWRBdDogc3RyaW5nO1xuICBqb3VybmFsPzogc3RyaW5nO1xuICBkb2k/OiBzdHJpbmc7XG4gIHVybDogc3RyaW5nO1xufSkge1xuICByZXR1cm4ge1xuICAgICdAY29udGV4dCc6ICdodHRwczovL3NjaGVtYS5vcmcnLFxuICAgICdAdHlwZSc6ICdTY2hvbGFybHlBcnRpY2xlJyxcbiAgICBoZWFkbGluZTogdGl0bGUsXG4gICAgYWJzdHJhY3QsXG4gICAgdXJsOiBgJHtzaXRlQ29uZmlnLnVybH0ke3VybH1gLFxuICAgIGRhdGVQdWJsaXNoZWQ6IHB1Ymxpc2hlZEF0LFxuICAgIGF1dGhvcjogYXV0aG9ycy5tYXAoYXV0aG9yID0+ICh7XG4gICAgICAnQHR5cGUnOiAnUGVyc29uJyxcbiAgICAgIG5hbWU6IGF1dGhvcixcbiAgICB9KSksXG4gICAgcHVibGlzaGVyOiBqb3VybmFsID8ge1xuICAgICAgJ0B0eXBlJzogJ09yZ2FuaXphdGlvbicsXG4gICAgICBuYW1lOiBqb3VybmFsLFxuICAgIH0gOiB1bmRlZmluZWQsXG4gICAgaWRlbnRpZmllcjogZG9pID8ge1xuICAgICAgJ0B0eXBlJzogJ1Byb3BlcnR5VmFsdWUnLFxuICAgICAgcHJvcGVydHlJRDogJ0RPSScsXG4gICAgICB2YWx1ZTogZG9pLFxuICAgIH0gOiB1bmRlZmluZWQsXG4gICAgbWFpbkVudGl0eU9mUGFnZToge1xuICAgICAgJ0B0eXBlJzogJ1dlYlBhZ2UnLFxuICAgICAgJ0BpZCc6IGAke3NpdGVDb25maWcudXJsfSR7dXJsfWAsXG4gICAgfSxcbiAgfTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGdlbmVyYXRlQnJlYWRjcnVtYlN0cnVjdHVyZWREYXRhKGl0ZW1zOiBBcnJheTx7IG5hbWU6IHN0cmluZzsgdXJsOiBzdHJpbmcgfT4pIHtcbiAgcmV0dXJuIHtcbiAgICAnQGNvbnRleHQnOiAnaHR0cHM6Ly9zY2hlbWEub3JnJyxcbiAgICAnQHR5cGUnOiAnQnJlYWRjcnVtYkxpc3QnLFxuICAgIGl0ZW1MaXN0RWxlbWVudDogaXRlbXMubWFwKChpdGVtLCBpbmRleCkgPT4gKHtcbiAgICAgICdAdHlwZSc6ICdMaXN0SXRlbScsXG4gICAgICBwb3NpdGlvbjogaW5kZXggKyAxLFxuICAgICAgbmFtZTogaXRlbS5uYW1lLFxuICAgICAgaXRlbTogYCR7c2l0ZUNvbmZpZy51cmx9JHtpdGVtLnVybH1gLFxuICAgIH0pKSxcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJzaXRlQ29uZmlnIiwibmFtZSIsImRlc2NyaXB0aW9uIiwidXJsIiwib2dJbWFnZSIsImF1dGhvciIsImVtYWlsIiwidHdpdHRlciIsImxpbmtlZGluIiwiZ2l0aHViIiwia2V5d29yZHMiLCJnZW5lcmF0ZU1ldGFkYXRhIiwidGl0bGUiLCJpbWFnZSIsInR5cGUiLCJwdWJsaXNoZWRUaW1lIiwibW9kaWZpZWRUaW1lIiwibWV0YVRpdGxlIiwibWV0YURlc2NyaXB0aW9uIiwibWV0YUltYWdlIiwibWV0YVVybCIsImFsbEtleXdvcmRzIiwiam9pbiIsImF1dGhvcnMiLCJjcmVhdG9yIiwicHVibGlzaGVyIiwicm9ib3RzIiwiaW5kZXgiLCJmb2xsb3ciLCJnb29nbGVCb3QiLCJvcGVuR3JhcGgiLCJsb2NhbGUiLCJzaXRlTmFtZSIsImltYWdlcyIsIndpZHRoIiwiaGVpZ2h0IiwiYWx0IiwiY2FyZCIsInNpdGUiLCJhbHRlcm5hdGVzIiwiY2Fub25pY2FsIiwib3RoZXIiLCJnZW5lcmF0ZVBlcnNvblN0cnVjdHVyZWREYXRhIiwic2FtZUFzIiwicmVwbGFjZSIsImpvYlRpdGxlIiwid29ya3NGb3IiLCJhbHVtbmlPZiIsImtub3dzQWJvdXQiLCJnZW5lcmF0ZVdlYnNpdGVTdHJ1Y3R1cmVkRGF0YSIsInBvdGVudGlhbEFjdGlvbiIsInRhcmdldCIsInVybFRlbXBsYXRlIiwiZ2VuZXJhdGVCbG9nUG9zdFN0cnVjdHVyZWREYXRhIiwicHVibGlzaGVkQXQiLCJ1cGRhdGVkQXQiLCJyZWFkaW5nVGltZSIsInRhZ3MiLCJoZWFkbGluZSIsImRhdGVQdWJsaXNoZWQiLCJkYXRlTW9kaWZpZWQiLCJ3b3JkQ291bnQiLCJ0aW1lUmVxdWlyZWQiLCJtYWluRW50aXR5T2ZQYWdlIiwiZ2VuZXJhdGVSZXNlYXJjaEFydGljbGVTdHJ1Y3R1cmVkRGF0YSIsImFic3RyYWN0Iiwiam91cm5hbCIsImRvaSIsIm1hcCIsInVuZGVmaW5lZCIsImlkZW50aWZpZXIiLCJwcm9wZXJ0eUlEIiwidmFsdWUiLCJnZW5lcmF0ZUJyZWFkY3J1bWJTdHJ1Y3R1cmVkRGF0YSIsIml0ZW1zIiwiaXRlbUxpc3RFbGVtZW50IiwiaXRlbSIsInBvc2l0aW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seo.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/GoogleAnalytics.tsx */ \"(ssr)/./src/components/analytics/GoogleAnalytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/WebVitals.tsx */ \"(ssr)/./src/components/analytics/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/GoogleAnalytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/analytics/GoogleAnalytics.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),\n/* harmony export */   trackDownload: () => (/* binding */ trackDownload),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackExternalLink: () => (/* binding */ trackExternalLink),\n/* harmony export */   trackFormSubmission: () => (/* binding */ trackFormSubmission),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ GoogleAnalytics,trackEvent,trackPageView,trackDownload,trackExternalLink,trackFormSubmission auto */ \n\nfunction GoogleAnalytics({ measurementId }) {\n    if (!measurementId || \"development\" !== 'production') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: `https://www.googletagmanager.com/gtag/js?id=${measurementId}`,\n                strategy: \"afterInteractive\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\analytics\\\\GoogleAnalytics.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics\",\n                strategy: \"afterInteractive\",\n                children: `\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${measurementId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        `\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\analytics\\\\GoogleAnalytics.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n// Analytics helper functions\nconst trackEvent = (action, category, label, value)=>{\n    if (false) {}\n};\nconst trackPageView = (url, title)=>{\n    if (false) {}\n};\nconst trackDownload = (filename)=>{\n    trackEvent('download', 'file', filename);\n};\nconst trackExternalLink = (url)=>{\n    trackEvent('click', 'external_link', url);\n};\nconst trackFormSubmission = (formName)=>{\n    trackEvent('submit', 'form', formName);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/GoogleAnalytics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/WebVitals.tsx":
/*!************************************************!*\
  !*** ./src/components/analytics/WebVitals.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals auto */ \n\nfunction sendToAnalytics(metric) {\n    // Send to your analytics service\n    if (false) {}\n    // Also log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n}\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/WebVitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const socialIcons = {\n        github: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        linkedin: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        twitter: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        email: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-muted/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\",\n                                        children: \"Prem Katuwal\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"muted\",\n                                    className: \"max-w-xs\",\n                                    children: \"Computer Science PhD student passionate about AI research, software development, and knowledge sharing.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: item.name\n                                        }, item.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/resume.pdf\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1\",\n                                            children: [\n                                                \"Resume \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Latest Posts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/research\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Publications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/projects\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Featured Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.github}/Katwal-77`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"GitHub\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.linkedin}/premkatuwal`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.twitter}/premkatuwal`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"Twitter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    \"aria-label\": \"Email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            href: \"/contact\",\n                                            size: \"sm\",\n                                            className: \"w-fit\",\n                                            children: \"Get in Touch\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            variant: \"muted\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" Prem Katuwal. Built with Next.js and Tailwind CSS.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-xs text-muted-foreground hover:text-primary transition-colors\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-xs text-muted-foreground hover:text-primary transition-colors\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvRm9vdGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUU2QjtBQUNnRDtBQUM3QjtBQUNRO0FBQ1M7QUFFMUQsU0FBU1U7SUFDZCxNQUFNQyxjQUFjLElBQUlDLE9BQU9DLFdBQVc7SUFFMUMsTUFBTUMsY0FBYztRQUNsQkMsUUFBUWQscUhBQU1BO1FBQ2RlLFVBQVVkLHFIQUFRQTtRQUNsQmUsU0FBU2QscUhBQU9BO1FBQ2hCZSxPQUFPZCxxSEFBSUE7SUFDYjtJQUVBLHFCQUNFLDhEQUFDZTtRQUFPQyxXQUFVO2tCQUNoQiw0RUFBQ0M7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFJRCxXQUFVOztzQ0FFYiw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDcEIsa0RBQUlBO29DQUFDc0IsTUFBSztvQ0FBSUYsV0FBVTs4Q0FDdkIsNEVBQUNHO3dDQUFLSCxXQUFVO2tEQUEwRjs7Ozs7Ozs7Ozs7OENBSTVHLDhEQUFDYixpRUFBVUE7b0NBQUNpQixTQUFRO29DQUFRSixXQUFVOzhDQUFXOzs7Ozs7Ozs7Ozs7c0NBT25ELDhEQUFDQzs0QkFBSUQsV0FBVTs7OENBQ2IsOERBQUNiLGlFQUFVQTtvQ0FBQ2lCLFNBQVE7OENBQUs7Ozs7Ozs4Q0FDekIsOERBQUNDO29DQUFJTCxXQUFVOzhDQUNaWiw0REFBZ0JBLENBQUNrQixHQUFHLENBQUMsQ0FBQ0MscUJBQ3JCLDhEQUFDM0Isa0RBQUlBOzRDQUVIc0IsTUFBTUssS0FBS0wsSUFBSTs0Q0FDZkYsV0FBVTtzREFFVE8sS0FBS0MsSUFBSTsyQ0FKTEQsS0FBS0wsSUFBSTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FXdEIsOERBQUNEOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ2IsaUVBQVVBO29DQUFDaUIsU0FBUTs4Q0FBSzs7Ozs7OzhDQUN6Qiw4REFBQ0M7b0NBQUlMLFdBQVU7O3NEQUNiLDhEQUFDcEIsa0RBQUlBOzRDQUNIc0IsTUFBSzs0Q0FDTE8sUUFBTzs0Q0FDUEMsS0FBSTs0Q0FDSlYsV0FBVTs7Z0RBQ1g7OERBQ1EsOERBQUNmLHFIQUFZQTtvREFBQ2UsV0FBVTs7Ozs7Ozs7Ozs7O3NEQUVqQyw4REFBQ3BCLGtEQUFJQTs0Q0FDSHNCLE1BQUs7NENBQ0xGLFdBQVU7c0RBQ1g7Ozs7OztzREFHRCw4REFBQ3BCLGtEQUFJQTs0Q0FDSHNCLE1BQUs7NENBQ0xGLFdBQVU7c0RBQ1g7Ozs7OztzREFHRCw4REFBQ3BCLGtEQUFJQTs0Q0FDSHNCLE1BQUs7NENBQ0xGLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FPTCw4REFBQ0M7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDYixpRUFBVUE7b0NBQUNpQixTQUFROzhDQUFLOzs7Ozs7OENBQ3pCLDhEQUFDSDtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ2QseURBQU1BO29EQUNMZ0IsTUFBTSxHQUFHYix3REFBWUEsQ0FBQ00sTUFBTSxDQUFDLFVBQVUsQ0FBQztvREFDeENTLFNBQVE7b0RBQ1JPLE1BQUs7b0RBQ0xDLFFBQVE7b0RBQ1JDLGNBQVc7OERBRVgsNEVBQUNoQyxxSEFBTUE7d0RBQUNtQixXQUFVOzs7Ozs7Ozs7Ozs4REFFcEIsOERBQUNkLHlEQUFNQTtvREFDTGdCLE1BQU0sR0FBR2Isd0RBQVlBLENBQUNPLFFBQVEsQ0FBQyxZQUFZLENBQUM7b0RBQzVDUSxTQUFRO29EQUNSTyxNQUFLO29EQUNMQyxRQUFRO29EQUNSQyxjQUFXOzhEQUVYLDRFQUFDL0IscUhBQVFBO3dEQUFDa0IsV0FBVTs7Ozs7Ozs7Ozs7OERBRXRCLDhEQUFDZCx5REFBTUE7b0RBQ0xnQixNQUFNLEdBQUdiLHdEQUFZQSxDQUFDUSxPQUFPLENBQUMsWUFBWSxDQUFDO29EQUMzQ08sU0FBUTtvREFDUk8sTUFBSztvREFDTEMsUUFBUTtvREFDUkMsY0FBVzs4REFFWCw0RUFBQzlCLHFIQUFPQTt3REFBQ2lCLFdBQVU7Ozs7Ozs7Ozs7OzhEQUVyQiw4REFBQ2QseURBQU1BO29EQUNMZ0IsTUFBSztvREFDTEUsU0FBUTtvREFDUk8sTUFBSztvREFDTEUsY0FBVzs4REFFWCw0RUFBQzdCLHFIQUFJQTt3REFBQ2dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUdwQiw4REFBQ2QseURBQU1BOzRDQUFDZ0IsTUFBSzs0Q0FBV1MsTUFBSzs0Q0FBS1gsV0FBVTtzREFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVExRCw4REFBQ0M7b0JBQUlELFdBQVU7O3NDQUNiLDhEQUFDYixpRUFBVUE7NEJBQUNpQixTQUFROztnQ0FBUTtnQ0FDdkJiO2dDQUFZOzs7Ozs7O3NDQUVqQiw4REFBQ1U7NEJBQUlELFdBQVU7OzhDQUNiLDhEQUFDcEIsa0RBQUlBO29DQUNIc0IsTUFBSztvQ0FDTEYsV0FBVTs4Q0FDWDs7Ozs7OzhDQUdELDhEQUFDcEIsa0RBQUlBO29DQUNIc0IsTUFBSztvQ0FDTEYsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRYiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwcmVta1xcRGVza3RvcFxccGZcXHNyY1xcY29tcG9uZW50c1xcbGF5b3V0XFxGb290ZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJztcbmltcG9ydCB7IEdpdGh1YiwgTGlua2VkaW4sIFR3aXR0ZXIsIE1haWwsIEV4dGVybmFsTGluayB9IGZyb20gJ2x1Y2lkZS1yZWFjdCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvQnV0dG9uJztcbmltcG9ydCB7IFR5cG9ncmFwaHkgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvVHlwb2dyYXBoeSc7XG5pbXBvcnQgeyBOQVZJR0FUSU9OX0lURU1TLCBTT0NJQUxfTElOS1MgfSBmcm9tICdAL2xpYi9jb25zdGFudHMnO1xuXG5leHBvcnQgZnVuY3Rpb24gRm9vdGVyKCkge1xuICBjb25zdCBjdXJyZW50WWVhciA9IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcblxuICBjb25zdCBzb2NpYWxJY29ucyA9IHtcbiAgICBnaXRodWI6IEdpdGh1YixcbiAgICBsaW5rZWRpbjogTGlua2VkaW4sXG4gICAgdHdpdHRlcjogVHdpdHRlcixcbiAgICBlbWFpbDogTWFpbCxcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxmb290ZXIgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYmctbXV0ZWQvNTBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG8gcHgtNCBweS0xMlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTQgZ2FwLThcIj5cbiAgICAgICAgICB7LyogQnJhbmQgU2VjdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQteGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXByaW1hcnkgdG8tYWNjZW50IGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50XCI+XG4gICAgICAgICAgICAgICAgUHJlbSBLYXR1d2FsXG4gICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJtdXRlZFwiIGNsYXNzTmFtZT1cIm1heC13LXhzXCI+XG4gICAgICAgICAgICAgIENvbXB1dGVyIFNjaWVuY2UgUGhEIHN0dWRlbnQgcGFzc2lvbmF0ZSBhYm91dCBBSSByZXNlYXJjaCwgXG4gICAgICAgICAgICAgIHNvZnR3YXJlIGRldmVsb3BtZW50LCBhbmQga25vd2xlZGdlIHNoYXJpbmcuXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+TmF2aWdhdGlvbjwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDxuYXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzcGFjZS15LTJcIj5cbiAgICAgICAgICAgICAge05BVklHQVRJT05fSVRFTVMubWFwKChpdGVtKSA9PiAoXG4gICAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICAgIGtleT17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgaHJlZj17aXRlbS5ocmVmfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICB7aXRlbS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBRdWljayBMaW5rcyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg2XCI+UXVpY2sgTGlua3M8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9yZXN1bWUucGRmXCJcbiAgICAgICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgICAgIHJlbD1cIm5vb3BlbmVyIG5vcmVmZXJyZXJcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9ycyBpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgUmVzdW1lIDxFeHRlcm5hbExpbmsgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmtcbiAgICAgICAgICAgICAgICBocmVmPVwiL2Jsb2dcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBMYXRlc3QgUG9zdHNcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCIvcmVzZWFyY2hcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBQdWJsaWNhdGlvbnNcbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICAgIGhyZWY9XCIvcHJvamVjdHNcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBGZWF0dXJlZCBQcm9qZWN0c1xuICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L25hdj5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiBDb250YWN0ICYgU29jaWFsICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIj5Db25uZWN0PC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHNwYWNlLXktM1wiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgaHJlZj17YCR7U09DSUFMX0xJTktTLmdpdGh1Yn0vS2F0d2FsLTc3YH1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICBleHRlcm5hbFxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkdpdEh1YlwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPEdpdGh1YiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBocmVmPXtgJHtTT0NJQUxfTElOS1MubGlua2VkaW59L3ByZW1rYXR1d2FsYH1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICBleHRlcm5hbFxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIkxpbmtlZEluXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8TGlua2VkaW4gY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgaHJlZj17YCR7U09DSUFMX0xJTktTLnR3aXR0ZXJ9L3ByZW1rYXR1d2FsYH1cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXG4gICAgICAgICAgICAgICAgICBleHRlcm5hbFxuICAgICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlR3aXR0ZXJcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxUd2l0dGVyIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIGhyZWY9XCJtYWlsdG86cHJlbWthdHdhbDk4N0BnbWFpbC5jb21cIlxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJpY29uXCJcbiAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJFbWFpbFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPE1haWwgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8QnV0dG9uIGhyZWY9XCIvY29udGFjdFwiIHNpemU9XCJzbVwiIGNsYXNzTmFtZT1cInctZml0XCI+XG4gICAgICAgICAgICAgICAgR2V0IGluIFRvdWNoXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBCb3R0b20gU2VjdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC04IHB0LTggYm9yZGVyLXQgZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIHNwYWNlLXktNCBzbTpzcGFjZS15LTBcIj5cbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwibXV0ZWRcIj5cbiAgICAgICAgICAgIMKpIHtjdXJyZW50WWVhcn0gUHJlbSBLYXR1d2FsLiBCdWlsdCB3aXRoIE5leHQuanMgYW5kIFRhaWx3aW5kIENTUy5cbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgIDxMaW5rXG4gICAgICAgICAgICAgIGhyZWY9XCIvcHJpdmFjeVwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGhvdmVyOnRleHQtcHJpbWFyeSB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIFByaXZhY3kgUG9saWN5XG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGlua1xuICAgICAgICAgICAgICBocmVmPVwiL3Rlcm1zXCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmQgaG92ZXI6dGV4dC1wcmltYXJ5IHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgVGVybXMgb2YgU2VydmljZVxuICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkxpbmsiLCJHaXRodWIiLCJMaW5rZWRpbiIsIlR3aXR0ZXIiLCJNYWlsIiwiRXh0ZXJuYWxMaW5rIiwiQnV0dG9uIiwiVHlwb2dyYXBoeSIsIk5BVklHQVRJT05fSVRFTVMiLCJTT0NJQUxfTElOS1MiLCJGb290ZXIiLCJjdXJyZW50WWVhciIsIkRhdGUiLCJnZXRGdWxsWWVhciIsInNvY2lhbEljb25zIiwiZ2l0aHViIiwibGlua2VkaW4iLCJ0d2l0dGVyIiwiZW1haWwiLCJmb290ZXIiLCJjbGFzc05hbWUiLCJkaXYiLCJocmVmIiwic3BhbiIsInZhcmlhbnQiLCJuYXYiLCJtYXAiLCJpdGVtIiwibmFtZSIsInRhcmdldCIsInJlbCIsInNpemUiLCJleHRlcm5hbCIsImFyaWEtbGFiZWwiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/layout/ThemeToggle.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const toggleMenu = ()=>setIsMenuOpen(!isMenuOpen);\n    const closeMenu = ()=>setIsMenuOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto flex h-16 items-center justify-between px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2 font-bold text-xl\",\n                        onClick: closeMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\",\n                            children: \"Prem Katuwal\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('text-sm font-medium transition-colors hover:text-primary', pathname === item.href ? 'text-primary' : 'text-muted-foreground'),\n                                children: item.name\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                href: \"/contact\",\n                                size: \"sm\",\n                                children: \"Get in Touch\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: toggleMenu,\n                                \"aria-label\": \"Toggle menu\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden border-t bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container mx-auto px-4 py-4 space-y-4\",\n                    children: [\n                        _lib_constants__WEBPACK_IMPORTED_MODULE_6__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('block text-sm font-medium transition-colors hover:text-primary', pathname === item.href ? 'text-primary' : 'text-muted-foreground'),\n                                onClick: closeMenu,\n                                children: item.name\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                href: \"/contact\",\n                                size: \"sm\",\n                                className: \"w-full\",\n                                children: \"Get in Touch\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/ThemeToggle.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/ThemeToggle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeSelector: () => (/* binding */ ThemeSelector),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useTheme */ \"(ssr)/./src/hooks/useTheme.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle,ThemeSelector auto */ \n\n\n\n\n\nfunction ThemeToggle({ className, showLabel = false }) {\n    const { theme, resolvedTheme, setTheme, isLoaded } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Prevent hydration mismatch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeToggle.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeToggle.useEffect\"], []);\n    // Don't render until mounted to prevent hydration mismatch\n    if (!mounted || !isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n            variant: \"ghost\",\n            size: \"icon\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('relative', className),\n            disabled: true,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"h-5 w-5\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"sr-only\",\n                    children: \"Loading theme...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this);\n    }\n    const themes = [\n        {\n            value: 'light',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Light'\n        },\n        {\n            value: 'dark',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Dark'\n        },\n        {\n            value: 'system',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'System'\n        }\n    ];\n    const currentTheme = themes.find((t)=>t.value === theme) || themes[0];\n    const Icon = currentTheme.icon;\n    const cycleTheme = ()=>{\n        const currentIndex = themes.findIndex((t)=>t.value === theme);\n        const nextIndex = (currentIndex + 1) % themes.length;\n        setTheme(themes[nextIndex].value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: cycleTheme,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('relative', className),\n        \"aria-label\": `Switch to ${themes[(themes.findIndex((t)=>t.value === theme) + 1) % themes.length].label.toLowerCase()} theme`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"h-5 w-5 transition-all\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 text-sm font-medium\",\n                children: currentTheme.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\nfunction ThemeSelector({ className }) {\n    const { theme, setTheme, isLoaded } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeSelector.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ThemeSelector.useEffect\"], []);\n    const themes = [\n        {\n            value: 'light',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'Light'\n        },\n        {\n            value: 'dark',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            label: 'Dark'\n        },\n        {\n            value: 'system',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'System'\n        }\n    ];\n    // Don't render until mounted to prevent hydration mismatch\n    if (!mounted || !isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center space-x-1', className),\n            children: themes.map(({ value, icon: Icon, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    disabled: true,\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-xs\",\n                            children: label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, value, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n            lineNumber: 89,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)('flex items-center space-x-1', className),\n        children: themes.map(({ value, icon: Icon, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                variant: theme === value ? 'secondary' : 'ghost',\n                size: \"sm\",\n                onClick: ()=>setTheme(value),\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                        lineNumber: 116,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, value, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n        lineNumber: 107,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', {\n    variants: {\n        variant: {\n            primary: 'bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/95 shadow-sm hover:shadow-md',\n            secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/90 shadow-sm',\n            outline: 'border-2 border-primary bg-background text-primary hover:bg-primary hover:text-primary-foreground active:bg-primary/95 shadow-sm hover:shadow-md',\n            ghost: 'text-foreground hover:bg-accent hover:text-accent-foreground active:bg-accent/90',\n            destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/95 shadow-sm'\n        },\n        size: {\n            sm: 'h-9 rounded-md px-3 text-xs font-medium',\n            md: 'h-10 px-4 py-2 text-sm font-medium',\n            lg: 'h-11 rounded-md px-8 text-base font-medium',\n            icon: 'h-10 w-10'\n        }\n    },\n    defaultVariants: {\n        variant: 'primary',\n        size: 'md'\n    }\n});\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, size, href, external, children, ...props }, ref)=>{\n    const Comp = href ? 'a' : 'button';\n    const linkProps = href ? {\n        href,\n        ...external && {\n            target: '_blank',\n            rel: 'noopener noreferrer'\n        }\n    } : {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...linkProps,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 59,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Typography.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Typography.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Typography: () => (/* binding */ Typography),\n/* harmony export */   typographyVariants: () => (/* binding */ typographyVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Typography,typographyVariants auto */ \n\n\n\nconst typographyVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('', {\n    variants: {\n        variant: {\n            h1: 'scroll-m-20 text-4xl font-bold tracking-tight lg:text-6xl xl:text-7xl leading-tight',\n            h2: 'scroll-m-20 border-b pb-3 text-3xl font-semibold tracking-tight first:mt-0 lg:text-4xl leading-tight',\n            h3: 'scroll-m-20 text-2xl font-semibold tracking-tight lg:text-3xl leading-tight',\n            h4: 'scroll-m-20 text-xl font-semibold tracking-tight lg:text-2xl leading-tight',\n            h5: 'scroll-m-20 text-lg font-semibold tracking-tight lg:text-xl leading-tight',\n            h6: 'scroll-m-20 text-base font-semibold tracking-tight lg:text-lg leading-tight',\n            p: 'leading-relaxed text-base lg:text-lg [&:not(:first-child)]:mt-6',\n            lead: 'text-xl lg:text-2xl text-muted-foreground leading-relaxed',\n            large: 'text-lg lg:text-xl font-semibold leading-relaxed',\n            small: 'text-sm font-medium leading-relaxed',\n            muted: 'text-sm lg:text-base text-muted-foreground leading-relaxed',\n            code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold'\n        }\n    },\n    defaultVariants: {\n        variant: 'p'\n    }\n});\nconst Typography = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, as, ...props }, ref)=>{\n    const Comp = as || getDefaultElement(variant);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(typographyVariants({\n            variant,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Typography.tsx\",\n        lineNumber: 40,\n        columnNumber: 7\n    }, undefined);\n});\nTypography.displayName = 'Typography';\nfunction getDefaultElement(variant) {\n    switch(variant){\n        case 'h1':\n            return 'h1';\n        case 'h2':\n            return 'h2';\n        case 'h3':\n            return 'h3';\n        case 'h4':\n            return 'h4';\n        case 'h5':\n            return 'h5';\n        case 'h6':\n            return 'h6';\n        case 'lead':\n        case 'large':\n        case 'small':\n        case 'muted':\n        case 'p':\n            return 'p';\n        case 'code':\n            return 'code';\n        default:\n            return 'p';\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Typography.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTheme.ts":
/*!*******************************!*\
  !*** ./src/hooks/useTheme.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTheme auto */ \nfunction useTheme() {\n    // Initialize with system theme to prevent hydration mismatch\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('light');\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Only run on client side to prevent hydration mismatch\n            if (true) return;\n            // Get theme from localStorage or default to system\n            try {\n                const savedTheme = localStorage.getItem('theme');\n                if (savedTheme && [\n                    'light',\n                    'dark',\n                    'system'\n                ].includes(savedTheme)) {\n                    setTheme(savedTheme);\n                }\n            } catch (error) {\n                console.warn('Failed to load theme from localStorage:', error);\n            }\n            // Set loaded after a brief delay to ensure DOM is ready\n            const timer = setTimeout({\n                \"useTheme.useEffect.timer\": ()=>{\n                    setIsLoaded(true);\n                }\n            }[\"useTheme.useEffect.timer\"], 100);\n            return ({\n                \"useTheme.useEffect\": ()=>clearTimeout(timer)\n            })[\"useTheme.useEffect\"];\n        }\n    }[\"useTheme.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Only run on client side and after component is loaded\n            if (true) return;\n            const root = window.document.documentElement;\n            // Remove previous theme classes\n            root.classList.remove('light', 'dark');\n            let currentTheme;\n            if (theme === 'system') {\n                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                root.classList.add(systemTheme);\n                currentTheme = systemTheme;\n            } else {\n                root.classList.add(theme);\n                currentTheme = theme;\n            }\n            setResolvedTheme(currentTheme);\n            // Set color scheme for better browser integration\n            root.style.colorScheme = currentTheme;\n        }\n    }[\"useTheme.useEffect\"], [\n        theme,\n        isLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Only run on client side and after component is loaded\n            if (true) return;\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"useTheme.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        const systemTheme = mediaQuery.matches ? 'dark' : 'light';\n                        const root = window.document.documentElement;\n                        root.classList.remove('light', 'dark');\n                        root.classList.add(systemTheme);\n                        setResolvedTheme(systemTheme);\n                        root.style.colorScheme = systemTheme;\n                    }\n                }\n            }[\"useTheme.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"useTheme.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"useTheme.useEffect\"];\n        }\n    }[\"useTheme.useEffect\"], [\n        theme,\n        isLoaded\n    ]);\n    const setThemeAndSave = (newTheme)=>{\n        setTheme(newTheme);\n        try {\n            localStorage.setItem('theme', newTheme);\n        } catch (error) {\n            console.warn('Failed to save theme to localStorage:', error);\n        }\n    };\n    return {\n        theme,\n        resolvedTheme,\n        setTheme: setThemeAndSave,\n        isLoaded,\n        toggleTheme: ()=>{\n            const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\n            setThemeAndSave(newTheme);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTheme.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_VARIANTS: () => (/* binding */ ANIMATION_VARIANTS),\n/* harmony export */   BLOG_CATEGORIES: () => (/* binding */ BLOG_CATEGORIES),\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   PROJECT_CATEGORIES: () => (/* binding */ PROJECT_CATEGORIES),\n/* harmony export */   PUBLICATION_TYPES: () => (/* binding */ PUBLICATION_TYPES),\n/* harmony export */   SITE_CONFIG: () => (/* binding */ SITE_CONFIG),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_COLORS: () => (/* binding */ THEME_COLORS),\n/* harmony export */   TIMELINE_TYPES: () => (/* binding */ TIMELINE_TYPES)\n/* harmony export */ });\n// Application constants\nconst SITE_CONFIG = {\n    name: 'Prem Katuwal - Portfolio',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n    author: 'Prem Katuwal',\n    keywords: [\n        'Computer Science',\n        'Master\\'s Student',\n        'AI Research',\n        'UESTC',\n        'Machine Learning',\n        'Software Development',\n        'Publications',\n        'Portfolio',\n        'Artificial Intelligence',\n        'Chengdu',\n        'China'\n    ]\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About',\n        href: '/about'\n    },\n    {\n        name: 'Journey',\n        href: '/journey'\n    },\n    {\n        name: 'Research',\n        href: '/research'\n    },\n    {\n        name: 'Projects',\n        href: '/projects'\n    },\n    {\n        name: 'Volunteering',\n        href: '/volunteering'\n    },\n    {\n        name: 'Blog',\n        href: '/blog'\n    },\n    {\n        name: 'Testimonials',\n        href: '/testimonials'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nconst SOCIAL_LINKS = {\n    github: 'https://github.com',\n    linkedin: 'https://linkedin.com/in',\n    twitter: 'https://twitter.com',\n    scholar: 'https://scholar.google.com/citations?user=',\n    orcid: 'https://orcid.org/'\n};\nconst PROJECT_CATEGORIES = {\n    'ai-ml': 'AI & Machine Learning',\n    'web-dev': 'Web Development',\n    'research-tools': 'Research Tools',\n    'open-source': 'Open Source'\n};\nconst PUBLICATION_TYPES = {\n    journal: 'Journal Article',\n    conference: 'Conference Paper',\n    preprint: 'Preprint',\n    thesis: 'Thesis'\n};\nconst BLOG_CATEGORIES = {\n    research: 'Research',\n    technology: 'Technology',\n    career: 'Career',\n    tutorials: 'Tutorials'\n};\nconst TIMELINE_TYPES = {\n    education: 'Education',\n    work: 'Work Experience',\n    research: 'Research',\n    volunteering: 'Volunteering'\n};\nconst CONTACT_INFO = {\n    email: '<EMAIL>',\n    location: 'Chengdu, China',\n    availability: 'Available for research collaborations and opportunities'\n};\nconst ANIMATION_VARIANTS = {\n    fadeIn: {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1\n        }\n    },\n    slideUp: {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideDown: {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideLeft: {\n        hidden: {\n            opacity: 0,\n            x: 20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    slideRight: {\n        hidden: {\n            opacity: 0,\n            x: -20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    scale: {\n        hidden: {\n            opacity: 0,\n            scale: 0.95\n        },\n        visible: {\n            opacity: 1,\n            scale: 1\n        }\n    }\n};\nconst BREAKPOINTS = {\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    '2xl': 1536\n};\nconst THEME_COLORS = {\n    primary: {\n        50: '#eff6ff',\n        100: '#dbeafe',\n        500: '#3b82f6',\n        600: '#2563eb',\n        700: '#1d4ed8',\n        900: '#1e3a8a'\n    },\n    secondary: {\n        50: '#f8fafc',\n        100: '#f1f5f9',\n        500: '#64748b',\n        600: '#475569',\n        700: '#334155',\n        900: '#0f172a'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes with clsx\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format date to readable string\n */ function formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(new Date(date));\n}\n/**\n * Calculate reading time for text content\n */ function calculateReadingTime(text) {\n    const wordsPerMinute = 200;\n    const words = text.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n}\n/**\n * Slugify text for URLs\n */ function slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '').replace(/[\\s_-]+/g, '-').replace(/^-+|-+$/g, '');\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n/**\n * Debounce function for search and other inputs\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Check if we're in development mode\n */ const isDevelopment = \"development\" === 'development';\n/**\n * Check if we're in production mode\n */ const isProduction = \"development\" === 'production';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBNkM7QUFDSjtBQUV6Qzs7Q0FFQyxHQUNNLFNBQVNFLEdBQUcsR0FBR0MsTUFBb0I7SUFDeEMsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsV0FBV0MsSUFBbUI7SUFDNUMsT0FBTyxJQUFJQyxLQUFLQyxjQUFjLENBQUMsU0FBUztRQUN0Q0MsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUCxHQUFHQyxNQUFNLENBQUMsSUFBSUMsS0FBS1A7QUFDckI7QUFFQTs7Q0FFQyxHQUNNLFNBQVNRLHFCQUFxQkMsSUFBWTtJQUMvQyxNQUFNQyxpQkFBaUI7SUFDdkIsTUFBTUMsUUFBUUYsS0FBS0csSUFBSSxHQUFHQyxLQUFLLENBQUMsT0FBT0MsTUFBTTtJQUM3QyxPQUFPQyxLQUFLQyxJQUFJLENBQUNMLFFBQVFEO0FBQzNCO0FBRUE7O0NBRUMsR0FDTSxTQUFTTyxRQUFRUixJQUFZO0lBQ2xDLE9BQU9BLEtBQ0pTLFdBQVcsR0FDWEMsT0FBTyxDQUFDLGFBQWEsSUFDckJBLE9BQU8sQ0FBQyxZQUFZLEtBQ3BCQSxPQUFPLENBQUMsWUFBWTtBQUN6QjtBQUVBOztDQUVDLEdBQ00sU0FBU0MsYUFBYVgsSUFBWSxFQUFFWSxTQUFpQjtJQUMxRCxJQUFJWixLQUFLSyxNQUFNLElBQUlPLFdBQVcsT0FBT1o7SUFDckMsT0FBT0EsS0FBS2EsS0FBSyxDQUFDLEdBQUdELFdBQVdGLE9BQU8sQ0FBQyxXQUFXLE1BQU07QUFDM0Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVNJLFNBQ2RDLElBQU8sRUFDUEMsSUFBWTtJQUVaLElBQUlDO0lBQ0osT0FBTyxDQUFDLEdBQUdDO1FBQ1RDLGFBQWFGO1FBQ2JBLFVBQVVHLFdBQVcsSUFBTUwsUUFBUUcsT0FBT0Y7SUFDNUM7QUFDRjtBQUVBOztDQUVDLEdBQ00sTUFBTUssZ0JBQWdCQyxrQkFBeUIsY0FBYztBQUVwRTs7Q0FFQyxHQUNNLE1BQU1DLGVBQWVELGtCQUF5QixhQUFhIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByZW1rXFxEZXNrdG9wXFxwZlxcc3JjXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHR5cGUgQ2xhc3NWYWx1ZSwgY2xzeCB9IGZyb20gJ2Nsc3gnO1xuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gJ3RhaWx3aW5kLW1lcmdlJztcblxuLyoqXG4gKiBVdGlsaXR5IGZ1bmN0aW9uIHRvIG1lcmdlIFRhaWx3aW5kIENTUyBjbGFzc2VzIHdpdGggY2xzeFxuICovXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKTtcbn1cblxuLyoqXG4gKiBGb3JtYXQgZGF0ZSB0byByZWFkYWJsZSBzdHJpbmdcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZTogc3RyaW5nIHwgRGF0ZSk6IHN0cmluZyB7XG4gIHJldHVybiBuZXcgSW50bC5EYXRlVGltZUZvcm1hdCgnZW4tVVMnLCB7XG4gICAgeWVhcjogJ251bWVyaWMnLFxuICAgIG1vbnRoOiAnbG9uZycsXG4gICAgZGF5OiAnbnVtZXJpYycsXG4gIH0pLmZvcm1hdChuZXcgRGF0ZShkYXRlKSk7XG59XG5cbi8qKlxuICogQ2FsY3VsYXRlIHJlYWRpbmcgdGltZSBmb3IgdGV4dCBjb250ZW50XG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBjYWxjdWxhdGVSZWFkaW5nVGltZSh0ZXh0OiBzdHJpbmcpOiBudW1iZXIge1xuICBjb25zdCB3b3Jkc1Blck1pbnV0ZSA9IDIwMDtcbiAgY29uc3Qgd29yZHMgPSB0ZXh0LnRyaW0oKS5zcGxpdCgvXFxzKy8pLmxlbmd0aDtcbiAgcmV0dXJuIE1hdGguY2VpbCh3b3JkcyAvIHdvcmRzUGVyTWludXRlKTtcbn1cblxuLyoqXG4gKiBTbHVnaWZ5IHRleHQgZm9yIFVSTHNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIHNsdWdpZnkodGV4dDogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIHRleHRcbiAgICAudG9Mb3dlckNhc2UoKVxuICAgIC5yZXBsYWNlKC9bXlxcd1xccy1dL2csICcnKVxuICAgIC5yZXBsYWNlKC9bXFxzXy1dKy9nLCAnLScpXG4gICAgLnJlcGxhY2UoL14tK3wtKyQvZywgJycpO1xufVxuXG4vKipcbiAqIFRydW5jYXRlIHRleHQgdG8gc3BlY2lmaWVkIGxlbmd0aFxuICovXG5leHBvcnQgZnVuY3Rpb24gdHJ1bmNhdGVUZXh0KHRleHQ6IHN0cmluZywgbWF4TGVuZ3RoOiBudW1iZXIpOiBzdHJpbmcge1xuICBpZiAodGV4dC5sZW5ndGggPD0gbWF4TGVuZ3RoKSByZXR1cm4gdGV4dDtcbiAgcmV0dXJuIHRleHQuc2xpY2UoMCwgbWF4TGVuZ3RoKS5yZXBsYWNlKC9cXHMrXFxTKiQvLCAnJykgKyAnLi4uJztcbn1cblxuLyoqXG4gKiBEZWJvdW5jZSBmdW5jdGlvbiBmb3Igc2VhcmNoIGFuZCBvdGhlciBpbnB1dHNcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGRlYm91bmNlPFQgZXh0ZW5kcyAoLi4uYXJnczogYW55W10pID0+IGFueT4oXG4gIGZ1bmM6IFQsXG4gIHdhaXQ6IG51bWJlclxuKTogKC4uLmFyZ3M6IFBhcmFtZXRlcnM8VD4pID0+IHZvaWQge1xuICBsZXQgdGltZW91dDogTm9kZUpTLlRpbWVvdXQ7XG4gIHJldHVybiAoLi4uYXJnczogUGFyYW1ldGVyczxUPikgPT4ge1xuICAgIGNsZWFyVGltZW91dCh0aW1lb3V0KTtcbiAgICB0aW1lb3V0ID0gc2V0VGltZW91dCgoKSA9PiBmdW5jKC4uLmFyZ3MpLCB3YWl0KTtcbiAgfTtcbn1cblxuLyoqXG4gKiBDaGVjayBpZiB3ZSdyZSBpbiBkZXZlbG9wbWVudCBtb2RlXG4gKi9cbmV4cG9ydCBjb25zdCBpc0RldmVsb3BtZW50ID0gcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdkZXZlbG9wbWVudCc7XG5cbi8qKlxuICogQ2hlY2sgaWYgd2UncmUgaW4gcHJvZHVjdGlvbiBtb2RlXG4gKi9cbmV4cG9ydCBjb25zdCBpc1Byb2R1Y3Rpb24gPSBwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nO1xuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJmb3JtYXREYXRlIiwiZGF0ZSIsIkludGwiLCJEYXRlVGltZUZvcm1hdCIsInllYXIiLCJtb250aCIsImRheSIsImZvcm1hdCIsIkRhdGUiLCJjYWxjdWxhdGVSZWFkaW5nVGltZSIsInRleHQiLCJ3b3Jkc1Blck1pbnV0ZSIsIndvcmRzIiwidHJpbSIsInNwbGl0IiwibGVuZ3RoIiwiTWF0aCIsImNlaWwiLCJzbHVnaWZ5IiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIiwidHJ1bmNhdGVUZXh0IiwibWF4TGVuZ3RoIiwic2xpY2UiLCJkZWJvdW5jZSIsImZ1bmMiLCJ3YWl0IiwidGltZW91dCIsImFyZ3MiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiaXNEZXZlbG9wbWVudCIsInByb2Nlc3MiLCJpc1Byb2R1Y3Rpb24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/web-vitals","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();