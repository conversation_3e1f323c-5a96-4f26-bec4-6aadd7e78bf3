# 🛠️ Complete Setup Guide - <PERSON><PERSON>'s Portfolio

This comprehensive guide will walk you through setting up the portfolio from scratch, including all development tools, environment configuration, and deployment setup.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10+, macOS 10.15+, or Linux
- **Node.js**: Version 18.0.0 or higher (LTS recommended)
- **npm**: Version 8.0.0 or higher (comes with Node.js)
- **Git**: Latest version for version control
- **Code Editor**: VS Code recommended with extensions

### Recommended VS Code Extensions
```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

## 🚀 Step-by-Step Setup

### 1. Clone and Install

```bash
# Clone the repository
git clone https://github.com/Katwal-77/pf.git
cd pf

# Install dependencies
npm install

# Verify installation
npm run type-check
npm run lint
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.example .env.local

# Edit the environment file
# Windows: notepad .env.local
# macOS/Linux: nano .env.local
```

#### Required Environment Variables

```env
# Site Configuration (Required)
NEXT_PUBLIC_SITE_URL=http://localhost:3002
NEXT_PUBLIC_SITE_NAME="Prem Katuwal - AI Researcher & Software Engineer"
CONTACT_EMAIL=<EMAIL>

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_VERCEL_ENV=development
```

#### Optional Environment Variables

```env
# Google Analytics (Recommended for production)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Vercel Analytics (Recommended for production)
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx

# Email Configuration (For contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Feature Flags (Optional)
NEXT_PUBLIC_ENABLE_BLOG=true
NEXT_PUBLIC_ENABLE_TESTIMONIALS=true
NEXT_PUBLIC_ENABLE_JOURNEY=true
NEXT_PUBLIC_ENABLE_CONTACT_FORM=true
NEXT_PUBLIC_SPEED_INSIGHTS=true
```

### 3. Development Server

```bash
# Start development server
npm run dev

# Server will start on http://localhost:3002
# The port is configured to avoid conflicts with other Next.js projects
```

### 4. Verify Setup

```bash
# Run all checks
npm run type-check    # TypeScript compilation
npm run lint          # ESLint checks
npm run format:check  # Prettier formatting
npm test              # Run test suite
npm run build         # Production build test
```

## 🔧 Development Workflow

### Daily Development Commands

```bash
# Start development
npm run dev

# Run tests in watch mode
npm run test:watch

# Format code
npm run format

# Fix linting issues
npm run lint:fix

# Type checking
npm run type-check
```

### Code Quality Checks

```bash
# Full quality check (run before committing)
npm run lint && npm run type-check && npm run format:check && npm test

# Or use the pre-commit hook (automatically runs on git commit)
git add .
git commit -m "Your commit message"
```

## 📊 Analytics Setup

### Google Analytics 4

1. **Create GA4 Property:**
   - Go to [Google Analytics](https://analytics.google.com)
   - Create new property for your domain
   - Copy the Measurement ID (G-XXXXXXXXXX)

2. **Configure Environment:**
```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

3. **Verify Tracking:**
   - Start your development server
   - Visit your site
   - Check Real-time reports in GA4

### Vercel Analytics (Production)

1. **Enable in Vercel Dashboard:**
   - Deploy to Vercel first
   - Go to Project → Analytics
   - Enable Web Analytics

2. **Add Environment Variable:**
```env
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx
```

## 📧 Email Configuration

### Gmail SMTP Setup

1. **Enable 2-Factor Authentication** on your Gmail account

2. **Generate App Password:**
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"

3. **Configure Environment:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
```

### Alternative Email Services

#### SendGrid
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
```

#### Mailgun
```env
SMTP_HOST=smtp.mailgun.org
SMTP_PORT=587
SMTP_USER=your-mailgun-username
SMTP_PASS=your-mailgun-password
```

## 🧪 Testing Setup

### Running Tests

```bash
# Run all tests
npm test

# Run specific test types
npm run test:accessibility  # Accessibility tests
npm run test:performance    # Performance tests
npm run test:integration    # Integration tests

# Generate coverage report
npm run test:coverage

# Run tests for CI/CD
npm run test:ci
```

### Test Configuration

The project includes comprehensive testing:
- **Unit Tests**: Component behavior and logic
- **Integration Tests**: User workflows and API endpoints
- **Accessibility Tests**: WCAG 2.1 AA compliance
- **Performance Tests**: Rendering performance and optimization

## 🚀 Deployment Setup

### Vercel Deployment (Recommended)

1. **Install Vercel CLI:**
```bash
npm install -g vercel
```

2. **Login to Vercel:**
```bash
vercel login
```

3. **Deploy:**
```bash
# Preview deployment
npm run deploy:preview

# Production deployment
npm run deploy
```

4. **Configure Environment Variables in Vercel:**
   - Go to Vercel Dashboard
   - Select your project
   - Settings → Environment Variables
   - Add all production environment variables

### GitHub Actions CI/CD

The project includes automated CI/CD pipeline:

1. **Required GitHub Secrets:**
```
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-org-id
VERCEL_PROJECT_ID=your-project-id
GA_MEASUREMENT_ID=G-XXXXXXXXXX
VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx
```

2. **Workflow Triggers:**
   - Push to main/master branch → Production deployment
   - Pull requests → Preview deployment
   - Manual trigger → Custom deployment

## 🔍 Monitoring Setup

### Health Monitoring

The portfolio includes built-in monitoring endpoints:

```bash
# Health check
curl http://localhost:3002/api/health

# Analytics data
curl http://localhost:3002/api/analytics

# Deployment information
curl http://localhost:3002/api/deployment
```

### Performance Monitoring

```bash
# Run Lighthouse audit
npm run lighthouse

# Analyze bundle size
npm run analyze

# Check Core Web Vitals
curl http://localhost:3002/api/analytics?metric=web_vitals
```

## 🛠️ Customization Guide

### Personal Information

Update your personal information in these files:

1. **`src/data/personal.json`** - Basic personal information
2. **`src/data/timeline.json`** - Professional timeline
3. **`src/data/journey.json`** - Personal journey milestones
4. **`src/lib/seo.ts`** - SEO configuration
5. **`public/manifest.json`** - PWA configuration

### Content Management

1. **Projects**: Add to `src/data/projects.json`
2. **Blog Posts**: Add to `src/data/blog.json`
3. **Research**: Add to `src/data/research.json`
4. **Testimonials**: Add to `src/data/testimonials.json`
5. **Volunteering**: Add to `src/data/volunteering.json`

### Styling Customization

1. **Colors**: Edit `tailwind.config.ts`
2. **Fonts**: Update `src/app/layout.tsx`
3. **Components**: Modify files in `src/components/`
4. **Global Styles**: Edit `src/app/globals.css`

## 🔧 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Kill process on port 3002
npx kill-port 3002

# Or use different port
npm run dev -- --port 3003
```

#### Build Failures
```bash
# Clear cache and reinstall
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

#### TypeScript Errors
```bash
# Check for type errors
npm run type-check

# Update TypeScript
npm update typescript @types/node @types/react
```

#### Performance Issues
```bash
# Analyze bundle
npm run analyze

# Check for large dependencies
npx bundle-analyzer .next/static/chunks/*.js
```

### Getting Help

1. **Check Documentation**: README.md, DEPLOYMENT.md
2. **Review Logs**: Check browser console and terminal output
3. **GitHub Issues**: Search existing issues or create new one
4. **Contact**: <EMAIL>

## 📚 Additional Resources

- **Next.js Documentation**: https://nextjs.org/docs
- **Tailwind CSS**: https://tailwindcss.com/docs
- **TypeScript**: https://www.typescriptlang.org/docs
- **Vercel Deployment**: https://vercel.com/docs
- **Google Analytics**: https://developers.google.com/analytics

---

**🎉 Setup Complete!** Your portfolio development environment is now ready. Start customizing and building your amazing portfolio!
