import { render } from '@/__tests__/utils/test-utils'
import { measurePerformance, generateMockProjects, generateMockBlogPosts } from '@/__tests__/utils/test-utils'
import { ProjectCard } from '@/components/features/projects/ProjectCard'
import { BlogCard } from '@/components/features/blog/BlogCard'

describe('Performance Tests', () => {
  describe('Component Rendering Performance', () => {
    it('should render ProjectCard efficiently', () => {
      const mockProjects = generateMockProjects(1)
      
      const renderTime = measurePerformance(() => {
        render(<ProjectCard project={mockProjects[0]} />)
      })
      
      // Should render in less than 50ms
      expect(renderTime).toBeLessThan(50)
    })

    it('should render multiple ProjectCards efficiently', () => {
      const mockProjects = generateMockProjects(10)
      
      const renderTime = measurePerformance(() => {
        render(
          <div>
            {mockProjects.map((project, index) => (
              <ProjectCard key={project.id} project={project} index={index} />
            ))}
          </div>
        )
      })
      
      // Should render 10 cards in less than 200ms
      expect(renderTime).toBeLessThan(200)
    })

    it('should render BlogCard efficiently', () => {
      const mockPosts = generateMockBlogPosts(1)
      
      const renderTime = measurePerformance(() => {
        render(<BlogCard post={mockPosts[0]} />)
      })
      
      // Should render in less than 50ms
      expect(renderTime).toBeLessThan(50)
    })

    it('should render multiple BlogCards efficiently', () => {
      const mockPosts = generateMockBlogPosts(10)
      
      const renderTime = measurePerformance(() => {
        render(
          <div>
            {mockPosts.map((post, index) => (
              <BlogCard key={post.id} post={post} index={index} />
            ))}
          </div>
        )
      })
      
      // Should render 10 cards in less than 200ms
      expect(renderTime).toBeLessThan(200)
    })
  })

  describe('Large Dataset Performance', () => {
    it('should handle large project lists efficiently', () => {
      const mockProjects = generateMockProjects(50)
      
      const renderTime = measurePerformance(() => {
        render(
          <div>
            {mockProjects.map((project, index) => (
              <ProjectCard key={project.id} project={project} index={index} />
            ))}
          </div>
        )
      })
      
      // Should render 50 cards in less than 1 second
      expect(renderTime).toBeLessThan(1000)
    })

    it('should handle large blog post lists efficiently', () => {
      const mockPosts = generateMockBlogPosts(50)
      
      const renderTime = measurePerformance(() => {
        render(
          <div>
            {mockPosts.map((post, index) => (
              <BlogCard key={post.id} post={post} index={index} />
            ))}
          </div>
        )
      })
      
      // Should render 50 cards in less than 1 second
      expect(renderTime).toBeLessThan(1000)
    })
  })

  describe('Memory Usage', () => {
    it('should not create memory leaks with repeated renders', () => {
      const mockProjects = generateMockProjects(5)
      
      // Measure initial memory
      const initialMemory = performance.memory?.usedJSHeapSize || 0
      
      // Render and unmount multiple times
      for (let i = 0; i < 10; i++) {
        const { unmount } = render(
          <div>
            {mockProjects.map((project, index) => (
              <ProjectCard key={project.id} project={project} index={index} />
            ))}
          </div>
        )
        unmount()
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = performance.memory?.usedJSHeapSize || 0
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be reasonable (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })

  describe('Animation Performance', () => {
    it('should handle hover animations efficiently', () => {
      const mockProjects = generateMockProjects(1)
      
      const { container } = render(<ProjectCard project={mockProjects[0]} />)
      const card = container.firstChild as HTMLElement
      
      const animationTime = measurePerformance(() => {
        // Simulate hover events
        for (let i = 0; i < 10; i++) {
          card.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }))
          card.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }))
        }
      })
      
      // Animation handling should be fast
      expect(animationTime).toBeLessThan(100)
    })
  })

  describe('Rendering Optimization', () => {
    it('should not re-render unnecessarily', () => {
      const mockProjects = generateMockProjects(1)
      let renderCount = 0
      
      const TestComponent = () => {
        renderCount++
        return <ProjectCard project={mockProjects[0]} />
      }
      
      const { rerender } = render(<TestComponent />)
      
      // Initial render
      expect(renderCount).toBe(1)
      
      // Re-render with same props
      rerender(<TestComponent />)
      
      // Should have rendered twice (React's behavior)
      expect(renderCount).toBe(2)
    })

    it('should handle prop changes efficiently', () => {
      const mockProjects = generateMockProjects(2)
      
      const { rerender } = render(<ProjectCard project={mockProjects[0]} />)
      
      const updateTime = measurePerformance(() => {
        rerender(<ProjectCard project={mockProjects[1]} />)
      })
      
      // Prop updates should be fast
      expect(updateTime).toBeLessThan(50)
    })
  })

  describe('Bundle Size Impact', () => {
    it('should not import unnecessary dependencies', () => {
      // This test ensures we're not accidentally importing large libraries
      const mockProjects = generateMockProjects(1)
      
      const renderTime = measurePerformance(() => {
        render(<ProjectCard project={mockProjects[0]} />)
      })
      
      // If we're importing heavy dependencies, render time would be higher
      expect(renderTime).toBeLessThan(100)
    })
  })

  describe('Concurrent Rendering', () => {
    it('should handle multiple simultaneous renders', async () => {
      const mockProjects = generateMockProjects(5)
      
      const renderPromises = Array.from({ length: 5 }, (_, i) => 
        new Promise<number>((resolve) => {
          const startTime = performance.now()
          render(<ProjectCard project={mockProjects[i]} />)
          const endTime = performance.now()
          resolve(endTime - startTime)
        })
      )
      
      const renderTimes = await Promise.all(renderPromises)
      
      // All renders should complete in reasonable time
      renderTimes.forEach(time => {
        expect(time).toBeLessThan(100)
      })
    })
  })
})

// Performance monitoring utilities
export const performanceMonitor = {
  measureRenderTime: (component: React.ReactElement) => {
    const start = performance.now()
    render(component)
    const end = performance.now()
    return end - start
  },
  
  measureMemoryUsage: () => {
    return performance.memory?.usedJSHeapSize || 0
  },
  
  profileComponent: (component: React.ReactElement, iterations = 10) => {
    const times: number[] = []
    
    for (let i = 0; i < iterations; i++) {
      const time = performanceMonitor.measureRenderTime(component)
      times.push(time)
    }
    
    return {
      average: times.reduce((sum, time) => sum + time, 0) / times.length,
      min: Math.min(...times),
      max: Math.max(...times),
      times,
    }
  },
}
