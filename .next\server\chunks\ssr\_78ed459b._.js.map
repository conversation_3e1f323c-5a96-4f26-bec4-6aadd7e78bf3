{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/AboutHero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/AboutHero.tsx <module evaluation>\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,uEACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/AboutHero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const AboutHero = registerClientReference(\n    function() { throw new Error(\"Attempted to call AboutHero() from the server but AboutHero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/AboutHero.tsx\",\n    \"AboutHero\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mDACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Timeline.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Timeline.tsx <module evaluation>\",\n    \"Timeline\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sEACA", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Timeline.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Timeline = registerClientReference(\n    function() { throw new Error(\"Attempted to call Timeline() from the server but Timeline is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Timeline.tsx\",\n    \"Timeline\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kDACA", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Stats.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Stats = registerClientReference(\n    function() { throw new Error(\"Attempted to call Stats() from the server but Stats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Stats.tsx <module evaluation>\",\n    \"Stats\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,mEACA", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Stats.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Stats = registerClientReference(\n    function() { throw new Error(\"Attempted to call Stats() from the server but Stats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Stats.tsx\",\n    \"Stats\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,+CACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Skills.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Skills = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skills() from the server but Skills is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Skills.tsx <module evaluation>\",\n    \"Skills\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,oEACA", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Skills.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Skills = registerClientReference(\n    function() { throw new Error(\"Attempted to call Skills() from the server but Skills is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/Skills.tsx\",\n    \"Skills\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,gDACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/features/resume/ResumeDownload.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ResumeDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumeDownload() from the server but ResumeDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx <module evaluation>\",\n    \"ResumeDownload\",\n);\nexport const ResumePreview = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumePreview() from the server but ResumePreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx <module evaluation>\",\n    \"ResumePreview\",\n);\nexport const ResumeSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumeSection() from the server but ResumeSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx <module evaluation>\",\n    \"ResumeSection\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mFACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,mFACA", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/features/resume/ResumeDownload.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const ResumeDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumeDownload() from the server but ResumeDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx\",\n    \"ResumeDownload\",\n);\nexport const ResumePreview = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumePreview() from the server but ResumePreview is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx\",\n    \"ResumePreview\",\n);\nexport const ResumeSection = registerClientReference(\n    function() { throw new Error(\"Attempted to call ResumeSection() from the server but ResumeSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/features/resume/ResumeDownload.tsx\",\n    \"ResumeSection\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/app/about/page.tsx"], "sourcesContent": ["import { Metadata } from 'next';\nimport { AboutHero } from '@/components/sections/AboutHero';\nimport { Timeline } from '@/components/sections/Timeline';\nimport { Stats } from '@/components/sections/Stats';\nimport { Skills } from '@/components/sections/Skills';\nimport { ResumeSection } from '@/components/features/resume/ResumeDownload';\nimport personalData from '@/data/personal.json';\nimport timelineData from '@/data/timeline.json';\nimport statsData from '@/data/stats.json';\nimport skillsData from '@/data/skills.json';\n\nexport const metadata: Metadata = {\n  title: 'About Me',\n  description: 'Learn about my academic journey, professional experience, and passion for AI research and community engagement.',\n  keywords: ['about', 'biography', 'education', 'experience', 'PhD student', 'computer science', 'AI research'],\n};\n\nexport default function AboutPage() {\n  return (\n    <div className=\"space-y-0\">\n      {/* About Hero Section */}\n      <AboutHero\n        name={personalData.name}\n        title={personalData.title}\n        bio={personalData.bio}\n        location={personalData.location}\n        profileImage={personalData.profile_image}\n        resumeUrl={personalData.resume_url}\n      />\n\n      {/* Stats Section */}\n      <Stats stats={statsData} />\n\n      {/* Skills Section */}\n      <Skills skillCategories={skillsData} />\n\n      {/* Timeline Section */}\n      <Timeline\n        events={timelineData}\n        title=\"My Academic & Professional Journey\"\n        description=\"From early community involvement to cutting-edge research, here's how my passion for technology and education has shaped my career path.\"\n      />\n\n      {/* Resume Section */}\n      <section className=\"py-16 bg-muted/30\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold mb-4\">Professional Resume</h2>\n              <p className=\"text-lg text-muted-foreground\">\n                Download my complete professional resume highlighting my education at UESTC,\n                software engineering experience, and research contributions.\n              </p>\n            </div>\n            <div className=\"flex justify-center\">\n              <ResumeSection />\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAS;QAAa;QAAa;QAAc;QAAe;QAAoB;KAAc;AAC/G;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,2IAAA,CAAA,YAAS;gBACR,MAAM,+FAAA,CAAA,UAAY,CAAC,IAAI;gBACvB,OAAO,+FAAA,CAAA,UAAY,CAAC,KAAK;gBACzB,KAAK,+FAAA,CAAA,UAAY,CAAC,GAAG;gBACrB,UAAU,+FAAA,CAAA,UAAY,CAAC,QAAQ;gBAC/B,cAAc,+FAAA,CAAA,UAAY,CAAC,aAAa;gBACxC,WAAW,+FAAA,CAAA,UAAY,CAAC,UAAU;;;;;;0BAIpC,8OAAC,uIAAA,CAAA,QAAK;gBAAC,OAAO,4FAAA,CAAA,UAAS;;;;;;0BAGvB,8OAAC,wIAAA,CAAA,SAAM;gBAAC,iBAAiB,6FAAA,CAAA,UAAU;;;;;;0BAGnC,8OAAC,0IAAA,CAAA,WAAQ;gBACP,QAAQ,+FAAA,CAAA,UAAY;gBACpB,OAAM;gBACN,aAAY;;;;;;0BAId,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;0CAK/C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0JAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5B", "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/node_modules/next/dist/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR' with { 'turbopack-transition': 'next-server-utility' }\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: (id: string | number) => unknown\ndeclare const __next_app_load_chunk__: (id: string | number) => Promise<unknown>\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base' with { 'turbopack-transition': 'next-server-utility' }\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AACA,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;IACzE,wBAAwB;AAWnF,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;IAEkD,wBAAwB;AAOhG,iCAAiC;;;;;;;;;;;;IAI/BM,WAAWC,0DAAAA;AACb,EAAC,QAAA;AAED,MAAA,OAAA;IAAc;IAAA,sCAA0C;YAAE,QAAA;YAAA;YAAA,MAAwB;gBAAsB,EAAC,UAAA;oBAAA;oBAAA,CAEzG;oBAAA,yDAA4D;wBAC5D,KAAO,KAAA,CAAMC;wBAAAA,QAAc;4BAAA,GAAIX,CAAAA,gBAAmB;4BAAA;yBAAA;;mBAChDY,YAAY;;iBACVC,MAAMZ,UAAUa,QAAQ;sBACxBC,IAAAA,CAAM,CAAA;YAAA;SAAA;;SACNC,UAAU;cACV,IAAA;YAAA,MAAA,4BAA2C;iBAC3CC,MAAAA,MAAY,EAAA;wBAAA;4BACZC,KAAAA,CAAAA,GAAAA,EAAU,0MAAVA,CAAAA,sBAAU,EAAA,MAAA,MAAA,MAAA,MAAA,EAAA,iBAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,GAAA,CAAA,KAAA,CAAA,KAAA,MAAA,CAAA,CAAA,EAAA,CAAA,EAAA,EAAA;4BACVC,OAAAA,iTAAU,EAAE,QAAA,CAAA,KAAA,CAAA,CAAA,EAAA,6SAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACd,MAAA,CAAA,YAAA,CAAA;wBACAC;qBAAAA,MAAU;gBACRC,YAAYnB;UACd;QAAA,UAAA;YAAA,IAAA;YAAA;SAAA;QACF,CAAE,YAAA;YAAA,IAAA;YAAA;SAAA", "ignoreList": [0], "debugId": null}}]}