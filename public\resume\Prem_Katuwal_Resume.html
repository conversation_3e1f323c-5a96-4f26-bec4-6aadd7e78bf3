<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prem <PERSON>wal - Resume</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.4;
            color: #333;
            max-width: 8.5in;
            margin: 0 auto;
            padding: 0.5in;
            background: white;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 15px;
        }
        
        .name {
            font-size: 28px;
            font-weight: bold;
            color: #1e40af;
            margin-bottom: 5px;
        }
        
        .title {
            font-size: 16px;
            color: #6b7280;
            margin-bottom: 8px;
        }
        
        .contact {
            font-size: 12px;
            color: #4b5563;
        }
        
        .contact a {
            color: #2563eb;
            text-decoration: none;
        }
        
        .section {
            margin-bottom: 18px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #1e40af;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 3px;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .experience-item, .education-item, .project-item {
            margin-bottom: 12px;
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 3px;
        }
        
        .item-title {
            font-weight: bold;
            color: #1f2937;
        }
        
        .item-company {
            color: #2563eb;
            font-weight: 600;
        }
        
        .item-date {
            color: #6b7280;
            font-size: 12px;
            font-style: italic;
        }
        
        .item-location {
            color: #6b7280;
            font-size: 12px;
        }
        
        .item-description {
            margin-top: 4px;
            font-size: 13px;
            line-height: 1.3;
        }
        
        .item-description ul {
            margin-left: 15px;
            margin-top: 3px;
        }
        
        .item-description li {
            margin-bottom: 2px;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            font-size: 13px;
        }
        
        .skill-category {
            margin-bottom: 8px;
        }
        
        .skill-category strong {
            color: #1f2937;
            display: block;
            margin-bottom: 2px;
        }
        
        .achievements {
            font-size: 13px;
        }
        
        .achievements ul {
            margin-left: 15px;
        }
        
        .achievements li {
            margin-bottom: 3px;
        }
        
        @media print {
            body {
                padding: 0.3in;
            }
            .section {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">PREM KATUWAL</div>
        <div class="title">AI Researcher & Software Engineer</div>
        <div class="title">Master's Student in Computer Science (AI) • UESTC (Ranked 3rd Globally in AI)</div>
        <div class="contact">
            📧 <a href="mailto:<EMAIL>"><EMAIL></a> • 
            🌐 <a href="https://premkatuwal.com">premkatuwal.com</a> • 
            💼 <a href="https://linkedin.com/in/premkatuwal">linkedin.com/in/premkatuwal</a> • 
            🔗 <a href="https://github.com/Katwal-77">github.com/Katwal-77</a><br>
            📍 Chengdu, China • 🎓 UESTC (University of Electronic Science and Technology of China)
        </div>
    </div>

    <div class="section">
        <div class="section-title">Professional Summary</div>
        <p style="font-size: 13px; line-height: 1.4;">
            Dedicated AI researcher and software engineer currently pursuing Master's in Computer Science (AI) at UESTC, 
            ranked 3rd globally in AI. Combines 2+ years of professional software engineering experience with cutting-edge 
            AI research. Proven track record in community impact through technology education and international collaboration. 
            Passionate about bridging academic research with practical applications to solve real-world problems.
        </p>
    </div>

    <div class="section">
        <div class="section-title">Education</div>
        <div class="education-item">
            <div class="item-header">
                <div>
                    <div class="item-title">Master of Science in Computer Science (Artificial Intelligence)</div>
                    <div class="item-company">University of Electronic Science and Technology of China (UESTC)</div>
                    <div class="item-location">Chengdu, China</div>
                </div>
                <div class="item-date">2023 - Present</div>
            </div>
            <div class="item-description">
                <ul>
                    <li><strong>Global Ranking:</strong> 3rd in AI worldwide (US News & World Report)</li>
                    <li><strong>Focus Areas:</strong> Machine Learning, Deep Learning, AI Applications, Research Methodology</li>
                    <li><strong>Research:</strong> Contributing to international AI research projects and publications</li>
                    <li><strong>Academic Excellence:</strong> Maintaining high academic standards in advanced AI coursework</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Professional Experience</div>
        <div class="experience-item">
            <div class="item-header">
                <div>
                    <div class="item-title">Software Engineer</div>
                    <div class="item-company">Tech Solutions Inc.</div>
                    <div class="item-location">Remote</div>
                </div>
                <div class="item-date">2022 - 2023</div>
            </div>
            <div class="item-description">
                <ul>
                    <li>Led development of scalable web applications using React, Node.js, and cloud technologies</li>
                    <li>Improved application performance by 35% through code optimization and efficient algorithms</li>
                    <li>Mentored junior developers and conducted code reviews to maintain high code quality</li>
                    <li>Collaborated with cross-functional teams to deliver projects on time and within budget</li>
                    <li>Implemented CI/CD pipelines and automated testing, reducing deployment time by 50%</li>
                </ul>
            </div>
        </div>
        
        <div class="experience-item">
            <div class="item-header">
                <div>
                    <div class="item-title">Junior Software Developer</div>
                    <div class="item-company">StartupTech Co.</div>
                    <div class="item-location">Remote</div>
                </div>
                <div class="item-date">2021 - 2022</div>
            </div>
            <div class="item-description">
                <ul>
                    <li>Developed and maintained web applications using modern JavaScript frameworks</li>
                    <li>Achieved 98% accuracy rate in bug fixes and feature implementations</li>
                    <li>Participated in agile development processes and sprint planning</li>
                    <li>Contributed to open-source projects and technical documentation</li>
                </ul>
            </div>
        </div>

        <div class="experience-item">
            <div class="item-header">
                <div>
                    <div class="item-title">UN Enumerator</div>
                    <div class="item-company">United Nations</div>
                    <div class="item-location">International</div>
                </div>
                <div class="item-date">2020 - 2021</div>
            </div>
            <div class="item-description">
                <ul>
                    <li>Conducted field data collection and analysis for international development projects</li>
                    <li>Collaborated with diverse international teams across multiple time zones</li>
                    <li>Maintained high data quality standards and met strict project deadlines</li>
                    <li>Gained valuable experience in cross-cultural communication and global perspectives</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Technical Skills</div>
        <div class="skills-grid">
            <div class="skill-category">
                <strong>Programming Languages:</strong>
                Python, JavaScript, TypeScript, Java, C++, SQL
            </div>
            <div class="skill-category">
                <strong>AI/ML Frameworks:</strong>
                TensorFlow, PyTorch, Scikit-learn, Pandas, NumPy
            </div>
            <div class="skill-category">
                <strong>Web Technologies:</strong>
                React, Next.js, Node.js, Express, HTML5, CSS3
            </div>
            <div class="skill-category">
                <strong>Databases:</strong>
                PostgreSQL, MongoDB, MySQL, Redis
            </div>
            <div class="skill-category">
                <strong>Cloud & DevOps:</strong>
                AWS, Docker, Git, CI/CD, Linux, Vercel
            </div>
            <div class="skill-category">
                <strong>Research Tools:</strong>
                Jupyter, LaTeX, MATLAB, R, Statistical Analysis
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Community Impact & Volunteering</div>
        <div class="experience-item">
            <div class="item-header">
                <div>
                    <div class="item-title">Technology Education Volunteer</div>
                    <div class="item-company">Community Tech Initiative</div>
                </div>
                <div class="item-date">2019 - Present</div>
            </div>
            <div class="item-description">
                <ul>
                    <li>Trained 300+ individuals in digital literacy and basic programming skills</li>
                    <li>Developed and delivered curriculum for technology education programs</li>
                    <li>Mentored aspiring developers and provided career guidance</li>
                    <li>Organized coding workshops and tech meetups in local communities</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Key Achievements</div>
        <div class="achievements">
            <ul>
                <li><strong>Academic Excellence:</strong> Admitted to UESTC's prestigious AI program (ranked 3rd globally)</li>
                <li><strong>Professional Growth:</strong> Promoted from Junior Developer to Software Engineer within 2 years</li>
                <li><strong>Performance Impact:</strong> Achieved 35% performance improvement in production applications</li>
                <li><strong>Community Impact:</strong> Successfully trained 300+ people in technology skills</li>
                <li><strong>International Experience:</strong> Worked with UN on global development projects</li>
                <li><strong>Technical Excellence:</strong> Maintained 98% accuracy rate in software development tasks</li>
                <li><strong>Leadership:</strong> Mentored junior developers and led technical initiatives</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <div class="section-title">Languages</div>
        <div style="font-size: 13px;">
            <strong>English:</strong> Fluent (Professional Working Proficiency) • 
            <strong>Chinese:</strong> Intermediate (Learning) • 
            <strong>Native Language:</strong> Fluent
        </div>
    </div>
</body>
</html>
