{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\n/**\n * Utility function to merge Tailwind CSS classes with clsx\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Format date to readable string\n */\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date));\n}\n\n/**\n * Calculate reading time for text content\n */\nexport function calculateReadingTime(text: string): number {\n  const wordsPerMinute = 200;\n  const words = text.trim().split(/\\s+/).length;\n  return Math.ceil(words / wordsPerMinute);\n}\n\n/**\n * Slugify text for URLs\n */\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '');\n}\n\n/**\n * Truncate text to specified length\n */\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n\n/**\n * Debounce function for search and other inputs\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n/**\n * Check if we're in development mode\n */\nexport const isDevelopment = process.env.NODE_ENV === 'development';\n\n/**\n * Check if we're in production mode\n */\nexport const isProduction = process.env.NODE_ENV === 'production';\n"], "names": [], "mappings": ";;;;;;;;;;AAkE6B;AAlE7B;AACA;;;AAKO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAKO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAKO,SAAS,qBAAqB,IAAY;IAC/C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAC7C,OAAO,KAAK,IAAI,CAAC,QAAQ;AAC3B;AAKO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAKO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,MAAM,gBAAgB,oDAAyB;AAK/C,MAAM,eAAe,oDAAyB", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        primary:\n          'bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm',\n        secondary:\n          'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n        outline:\n          'border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        destructive:\n          'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm',\n      },\n      size: {\n        sm: 'h-9 rounded-md px-3 text-xs',\n        md: 'h-10 px-4 py-2',\n        lg: 'h-11 rounded-md px-8 text-base',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  href?: string;\n  external?: boolean;\n  children: React.ReactNode;\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, href, external, children, ...props }, ref) => {\n    const Comp = href ? 'a' : 'button';\n    const linkProps = href\n      ? {\n          href,\n          ...(external && {\n            target: '_blank',\n            rel: 'noopener noreferrer',\n          }),\n        }\n      : {};\n\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref as any}\n        {...linkProps}\n        {...props}\n      >\n        {children}\n      </Comp>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,mQACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,SACE;YACF,OAAO;YACP,aACE;QACJ;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OACtB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACjE,MAAM,OAAO,OAAO,MAAM;IAC1B,MAAM,YAAY,OACd;QACE;QACA,GAAI,YAAY;YACd,QAAQ;YACR,KAAK;QACP,CAAC;IACH,IACA,CAAC;IAEL,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,SAAS;QACZ,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/hooks/useTheme.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport type { Theme } from '@/types';\n\nexport function useTheme() {\n  const [theme, setTheme] = useState<Theme>('system');\n  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');\n\n  useEffect(() => {\n    // Get theme from localStorage or default to system\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    const root = window.document.documentElement;\n    \n    // Remove previous theme classes\n    root.classList.remove('light', 'dark');\n\n    if (theme === 'system') {\n      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')\n        .matches\n        ? 'dark'\n        : 'light';\n      \n      root.classList.add(systemTheme);\n      setResolvedTheme(systemTheme);\n    } else {\n      root.classList.add(theme);\n      setResolvedTheme(theme);\n    }\n  }, [theme]);\n\n  useEffect(() => {\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    \n    const handleChange = () => {\n      if (theme === 'system') {\n        const systemTheme = mediaQuery.matches ? 'dark' : 'light';\n        const root = window.document.documentElement;\n        root.classList.remove('light', 'dark');\n        root.classList.add(systemTheme);\n        setResolvedTheme(systemTheme);\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  const setThemeAndSave = (newTheme: Theme) => {\n    setTheme(newTheme);\n    localStorage.setItem('theme', newTheme);\n  };\n\n  return {\n    theme,\n    resolvedTheme,\n    setTheme: setThemeAndSave,\n    toggleTheme: () => {\n      const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\n      setThemeAndSave(newTheme);\n    },\n  };\n}\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAKO,SAAS;;IACd,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAErE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,mDAAmD;YACnD,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,cAAc;gBAAC;gBAAS;gBAAQ;aAAS,CAAC,QAAQ,CAAC,aAAa;gBAClE,SAAS;YACX;QACF;6BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;YAE5C,gCAAgC;YAChC,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;YAE/B,IAAI,UAAU,UAAU;gBACtB,MAAM,cAAc,OAAO,UAAU,CAAC,gCACnC,OAAO,GACN,SACA;gBAEJ,KAAK,SAAS,CAAC,GAAG,CAAC;gBACnB,iBAAiB;YACnB,OAAO;gBACL,KAAK,SAAS,CAAC,GAAG,CAAC;gBACnB,iBAAiB;YACnB;QACF;6BAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,kCAAkC;YAClC,MAAM,aAAa,OAAO,UAAU,CAAC;YAErC,MAAM;mDAAe;oBACnB,IAAI,UAAU,UAAU;wBACtB,MAAM,cAAc,WAAW,OAAO,GAAG,SAAS;wBAClD,MAAM,OAAO,OAAO,QAAQ,CAAC,eAAe;wBAC5C,KAAK,SAAS,CAAC,MAAM,CAAC,SAAS;wBAC/B,KAAK,SAAS,CAAC,GAAG,CAAC;wBACnB,iBAAiB;oBACnB;gBACF;;YAEA,WAAW,gBAAgB,CAAC,UAAU;YACtC;sCAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;;QACxD;6BAAG;QAAC;KAAM;IAEV,MAAM,kBAAkB,CAAC;QACvB,SAAS;QACT,aAAa,OAAO,CAAC,SAAS;IAChC;IAEA,OAAO;QACL;QACA;QACA,UAAU;QACV,aAAa;YACX,MAAM,WAAW,kBAAkB,UAAU,SAAS;YACtD,gBAAgB;QAClB;IACF;AACF;GAhEgB", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/layout/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { <PERSON>, <PERSON>, Monitor } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { useTheme } from '@/hooks/useTheme';\nimport { cn } from '@/lib/utils';\n\ninterface ThemeToggleProps {\n  className?: string;\n  showLabel?: boolean;\n}\n\nexport function ThemeToggle({ className, showLabel = false }: ThemeToggleProps) {\n  const { theme, resolvedTheme, setTheme } = useTheme();\n\n  const themes = [\n    { value: 'light' as const, icon: Sun, label: 'Light' },\n    { value: 'dark' as const, icon: Moon, label: 'Dark' },\n    { value: 'system' as const, icon: Monitor, label: 'System' },\n  ];\n\n  const currentTheme = themes.find(t => t.value === theme) || themes[0];\n  const Icon = currentTheme.icon;\n\n  const cycleTheme = () => {\n    const currentIndex = themes.findIndex(t => t.value === theme);\n    const nextIndex = (currentIndex + 1) % themes.length;\n    setTheme(themes[nextIndex]!.value);\n  };\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={cycleTheme}\n      className={cn('relative', className)}\n      aria-label={`Switch to ${themes[(themes.findIndex(t => t.value === theme) + 1) % themes.length]!.label.toLowerCase()} theme`}\n    >\n      <Icon className=\"h-5 w-5 transition-all\" />\n      {showLabel && (\n        <span className=\"ml-2 text-sm font-medium\">\n          {currentTheme.label}\n        </span>\n      )}\n      <span className=\"sr-only\">Toggle theme</span>\n    </Button>\n  );\n}\n\nexport function ThemeSelector({ className }: { className?: string }) {\n  const { theme, setTheme } = useTheme();\n\n  const themes = [\n    { value: 'light' as const, icon: Sun, label: 'Light' },\n    { value: 'dark' as const, icon: Moon, label: 'Dark' },\n    { value: 'system' as const, icon: Monitor, label: 'System' },\n  ];\n\n  return (\n    <div className={cn('flex items-center space-x-1', className)}>\n      {themes.map(({ value, icon: Icon, label }) => (\n        <Button\n          key={value}\n          variant={theme === value ? 'secondary' : 'ghost'}\n          size=\"sm\"\n          onClick={() => setTheme(value)}\n          className=\"flex items-center space-x-2\"\n        >\n          <Icon className=\"h-4 w-4\" />\n          <span className=\"text-xs\">{label}</span>\n        </Button>\n      ))}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,YAAY,EAAE,SAAS,EAAE,YAAY,KAAK,EAAoB;;IAC5E,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAElD,MAAM,SAAS;QACb;YAAE,OAAO;YAAkB,MAAM,mMAAA,CAAA,MAAG;YAAE,OAAO;QAAQ;QACrD;YAAE,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAO;QACpD;YAAE,OAAO;YAAmB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAS;KAC5D;IAED,MAAM,eAAe,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,UAAU,MAAM,CAAC,EAAE;IACrE,MAAM,OAAO,aAAa,IAAI;IAE9B,MAAM,aAAa;QACjB,MAAM,eAAe,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;QACvD,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,OAAO,MAAM;QACpD,SAAS,MAAM,CAAC,UAAU,CAAE,KAAK;IACnC;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAC1B,cAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,OAAO,SAAS,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK,SAAS,CAAC,IAAI,OAAO,MAAM,CAAC,CAAE,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;;0BAE5H,6LAAC;gBAAK,WAAU;;;;;;YACf,2BACC,6LAAC;gBAAK,WAAU;0BACb,aAAa,KAAK;;;;;;0BAGvB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GAnCgB;;QAC6B,2HAAA,CAAA,WAAQ;;;KADrC;AAqCT,SAAS,cAAc,EAAE,SAAS,EAA0B;;IACjE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,WAAQ,AAAD;IAEnC,MAAM,SAAS;QACb;YAAE,OAAO;YAAkB,MAAM,mMAAA,CAAA,MAAG;YAAE,OAAO;QAAQ;QACrD;YAAE,OAAO;YAAiB,MAAM,qMAAA,CAAA,OAAI;YAAE,OAAO;QAAO;QACpD;YAAE,OAAO;YAAmB,MAAM,2MAAA,CAAA,UAAO;YAAE,OAAO;QAAS;KAC5D;IAED,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;kBAC/C,OAAO,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,iBACvC,6LAAC,qIAAA,CAAA,SAAM;gBAEL,SAAS,UAAU,QAAQ,cAAc;gBACzC,MAAK;gBACL,SAAS,IAAM,SAAS;gBACxB,WAAU;;kCAEV,6LAAC;wBAAK,WAAU;;;;;;kCAChB,6LAAC;wBAAK,WAAU;kCAAW;;;;;;;eAPtB;;;;;;;;;;AAYf;IAzBgB;;QACc,2HAAA,CAAA,WAAQ;;;MADtB", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/lib/constants.ts"], "sourcesContent": ["// Application constants\n\nexport const SITE_CONFIG = {\n  name: '<PERSON><PERSON>',\n  description: 'Master\\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',\n  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n  author: '<PERSON><PERSON>',\n  keywords: [\n    'Computer Science',\n    'Master\\'s Student',\n    'AI Research',\n    'UESTC',\n    'Machine Learning',\n    'Software Development',\n    'Publications',\n    'Portfolio',\n    'Artificial Intelligence',\n    'Chengdu',\n    'China',\n  ],\n} as const;\n\nexport const NAVIGATION_ITEMS = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Journey', href: '/journey' },\n  { name: 'Research', href: '/research' },\n  { name: 'Projects', href: '/projects' },\n  { name: 'Volunteering', href: '/volunteering' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Testimonials', href: '/testimonials' },\n  { name: 'Contact', href: '/contact' },\n] as const;\n\nexport const SOCIAL_LINKS = {\n  github: 'https://github.com',\n  linkedin: 'https://linkedin.com/in',\n  twitter: 'https://twitter.com',\n  scholar: 'https://scholar.google.com/citations?user=',\n  orcid: 'https://orcid.org/',\n} as const;\n\nexport const PROJECT_CATEGORIES = {\n  'ai-ml': 'AI & Machine Learning',\n  'web-dev': 'Web Development',\n  'research-tools': 'Research Tools',\n  'open-source': 'Open Source',\n} as const;\n\nexport const PUBLICATION_TYPES = {\n  journal: 'Journal Article',\n  conference: 'Conference Paper',\n  preprint: 'Preprint',\n  thesis: 'Thesis',\n} as const;\n\nexport const BLOG_CATEGORIES = {\n  research: 'Research',\n  technology: 'Technology',\n  career: 'Career',\n  tutorials: 'Tutorials',\n} as const;\n\nexport const TIMELINE_TYPES = {\n  education: 'Education',\n  work: 'Work Experience',\n  research: 'Research',\n  volunteering: 'Volunteering',\n} as const;\n\nexport const CONTACT_INFO = {\n  email: '<EMAIL>',\n  location: 'Chengdu, China',\n  availability: 'Available for research collaborations and opportunities',\n} as const;\n\nexport const ANIMATION_VARIANTS = {\n  fadeIn: {\n    hidden: { opacity: 0 },\n    visible: { opacity: 1 },\n  },\n  slideUp: {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  },\n  slideDown: {\n    hidden: { opacity: 0, y: -20 },\n    visible: { opacity: 1, y: 0 },\n  },\n  slideLeft: {\n    hidden: { opacity: 0, x: 20 },\n    visible: { opacity: 1, x: 0 },\n  },\n  slideRight: {\n    hidden: { opacity: 0, x: -20 },\n    visible: { opacity: 1, x: 0 },\n  },\n  scale: {\n    hidden: { opacity: 0, scale: 0.95 },\n    visible: { opacity: 1, scale: 1 },\n  },\n} as const;\n\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const;\n\nexport const THEME_COLORS = {\n  primary: {\n    50: '#eff6ff',\n    100: '#dbeafe',\n    500: '#3b82f6',\n    600: '#2563eb',\n    700: '#1d4ed8',\n    900: '#1e3a8a',\n  },\n  secondary: {\n    50: '#f8fafc',\n    100: '#f1f5f9',\n    500: '#64748b',\n    600: '#475569',\n    700: '#334155',\n    900: '#0f172a',\n  },\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;;AAKjB;AAHA,MAAM,cAAc;IACzB,MAAM;IACN,aAAa;IACb,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI;IACzC,QAAQ;IACR,UAAU;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;AACH;AAEO,MAAM,mBAAmB;IAC9B;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEM,MAAM,eAAe;IAC1B,QAAQ;IACR,UAAU;IACV,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAEO,MAAM,qBAAqB;IAChC,SAAS;IACT,WAAW;IACX,kBAAkB;IAClB,eAAe;AACjB;AAEO,MAAM,oBAAoB;IAC/B,SAAS;IACT,YAAY;IACZ,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,kBAAkB;IAC7B,UAAU;IACV,YAAY;IACZ,QAAQ;IACR,WAAW;AACb;AAEO,MAAM,iBAAiB;IAC5B,WAAW;IACX,MAAM;IACN,UAAU;IACV,cAAc;AAChB;AAEO,MAAM,eAAe;IAC1B,OAAO;IACP,UAAU;IACV,cAAc;AAChB;AAEO,MAAM,qBAAqB;IAChC,QAAQ;QACN,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YAAE,SAAS;QAAE;IACxB;IACA,SAAS;QACP,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,YAAY;QACV,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,OAAO;QACL,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAK;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;IAClC;AACF;AAEO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAEO,MAAM,eAAe;IAC1B,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,WAAW;QACT,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Menu, X } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { ThemeToggle } from './ThemeToggle';\nimport { ResumeDownload } from '@/components/features/resume/ResumeDownload';\nimport { NAVIGATION_ITEMS } from '@/lib/constants';\nimport { cn } from '@/lib/utils';\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const pathname = usePathname();\n\n  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);\n  const closeMenu = () => setIsMenuOpen(false);\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto flex h-16 items-center justify-between px-4\">\n        {/* Logo */}\n        <Link\n          href=\"/\"\n          className=\"flex items-center space-x-2 font-bold text-xl\"\n          onClick={closeMenu}\n        >\n          <span className=\"bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n            Portfolio\n          </span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-6\">\n          {NAVIGATION_ITEMS.map((item) => (\n            <Link\n              key={item.href}\n              href={item.href}\n              className={cn(\n                'text-sm font-medium transition-colors hover:text-primary',\n                pathname === item.href\n                  ? 'text-primary'\n                  : 'text-muted-foreground'\n              )}\n            >\n              {item.name}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Desktop Actions */}\n        <div className=\"hidden md:flex items-center space-x-2\">\n          <ThemeToggle />\n          <Button href=\"/contact\" size=\"sm\">\n            Get in Touch\n          </Button>\n        </div>\n\n        {/* Mobile Menu Button */}\n        <div className=\"flex items-center space-x-2 md:hidden\">\n          <ThemeToggle />\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n          >\n            {isMenuOpen ? (\n              <X className=\"h-5 w-5\" />\n            ) : (\n              <Menu className=\"h-5 w-5\" />\n            )}\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isMenuOpen && (\n        <div className=\"md:hidden border-t bg-background\">\n          <nav className=\"container mx-auto px-4 py-4 space-y-4\">\n            {NAVIGATION_ITEMS.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  'block text-sm font-medium transition-colors hover:text-primary',\n                  pathname === item.href\n                    ? 'text-primary'\n                    : 'text-muted-foreground'\n                )}\n                onClick={closeMenu}\n              >\n                {item.name}\n              </Link>\n            ))}\n            <div className=\"pt-4 border-t\">\n              <Button href=\"/contact\" size=\"sm\" className=\"w-full\">\n                Get in Touch\n              </Button>\n            </div>\n          </nav>\n        </div>\n      )}\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AAEA;AACA;;;AAVA;;;;;;;;;AAYO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,aAAa,IAAM,cAAc,CAAC;IACxC,MAAM,YAAY,IAAM,cAAc;IAEtC,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;wBACV,SAAS;kCAET,cAAA,6LAAC;4BAAK,WAAU;sCAAwE;;;;;;;;;;;kCAM1F,6LAAC;wBAAI,WAAU;kCACZ,0HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,aAAa,KAAK,IAAI,GAClB,iBACA;0CAGL,KAAK,IAAI;+BATL,KAAK,IAAI;;;;;;;;;;kCAepB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8IAAA,CAAA,cAAW;;;;;0CACZ,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAW,MAAK;0CAAK;;;;;;;;;;;;kCAMpC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,8IAAA,CAAA,cAAW;;;;;0CACZ,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,cAAW;0CAEV,2BACC,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAEb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAOvB,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,0HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,aAAa,KAAK,IAAI,GAClB,iBACA;gCAEN,SAAS;0CAER,KAAK,IAAI;+BAVL,KAAK,IAAI;;;;;sCAalB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAW,MAAK;gCAAK,WAAU;0CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnE;GA9FgB;;QAEG,qIAAA,CAAA,cAAW;;;KAFd", "debugId": null}}, {"offset": {"line": 789, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/ui/Typography.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst typographyVariants = cva('', {\n  variants: {\n    variant: {\n      h1: 'scroll-m-20 text-4xl font-bold tracking-tight lg:text-5xl',\n      h2: 'scroll-m-20 border-b pb-2 text-3xl font-semibold tracking-tight first:mt-0',\n      h3: 'scroll-m-20 text-2xl font-semibold tracking-tight',\n      h4: 'scroll-m-20 text-xl font-semibold tracking-tight',\n      h5: 'scroll-m-20 text-lg font-semibold tracking-tight',\n      h6: 'scroll-m-20 text-base font-semibold tracking-tight',\n      p: 'leading-7 [&:not(:first-child)]:mt-6',\n      lead: 'text-xl text-muted-foreground',\n      large: 'text-lg font-semibold',\n      small: 'text-sm font-medium leading-none',\n      muted: 'text-sm text-muted-foreground',\n      code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold',\n    },\n  },\n  defaultVariants: {\n    variant: 'p',\n  },\n});\n\nexport interface TypographyProps\n  extends React.HTMLAttributes<HTMLElement>,\n    VariantProps<typeof typographyVariants> {\n  as?: keyof JSX.IntrinsicElements;\n}\n\nconst Typography = forwardRef<HTMLElement, TypographyProps>(\n  ({ className, variant, as, ...props }, ref) => {\n    const Comp = as || getDefaultElement(variant);\n    \n    return (\n      <Comp\n        className={cn(typographyVariants({ variant, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\n\nTypography.displayName = 'Typography';\n\nfunction getDefaultElement(variant: TypographyProps['variant']) {\n  switch (variant) {\n    case 'h1':\n      return 'h1';\n    case 'h2':\n      return 'h2';\n    case 'h3':\n      return 'h3';\n    case 'h4':\n      return 'h4';\n    case 'h5':\n      return 'h5';\n    case 'h6':\n      return 'h6';\n    case 'lead':\n    case 'large':\n    case 'small':\n    case 'muted':\n    case 'p':\n      return 'p';\n    case 'code':\n      return 'code';\n    default:\n      return 'p';\n  }\n}\n\nexport { Typography, typographyVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,qBAAqB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EAAE,IAAI;IACjC,UAAU;QACR,SAAS;YACP,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,GAAG;YACH,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAQA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAC1B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE,EAAE,GAAG,OAAO,EAAE;IACrC,MAAM,OAAO,MAAM,kBAAkB;IAErC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;YAAE;YAAS;QAAU;QACtD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAGF,WAAW,WAAW,GAAG;AAEzB,SAAS,kBAAkB,OAAmC;IAC5D,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 879, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport { Github, Linkedin, Twitter, Mail, ExternalLink } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { Typography } from '@/components/ui/Typography';\nimport { NAVIGATION_ITEMS, SOCIAL_LINKS } from '@/lib/constants';\n\nexport function Footer() {\n  const currentYear = new Date().getFullYear();\n\n  const socialIcons = {\n    github: Github,\n    linkedin: Linkedin,\n    twitter: Twitter,\n    email: Mail,\n  };\n\n  return (\n    <footer className=\"border-t bg-muted/50\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand Section */}\n          <div className=\"space-y-4\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <span className=\"font-bold text-xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                Portfolio\n              </span>\n            </Link>\n            <Typography variant=\"muted\" className=\"max-w-xs\">\n              Computer Science PhD student passionate about AI research, \n              software development, and knowledge sharing.\n            </Typography>\n          </div>\n\n          {/* Navigation */}\n          <div className=\"space-y-4\">\n            <Typography variant=\"h6\">Navigation</Typography>\n            <nav className=\"flex flex-col space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </nav>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <Typography variant=\"h6\">Quick Links</Typography>\n            <nav className=\"flex flex-col space-y-2\">\n              <Link\n                href=\"/resume.pdf\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1\"\n              >\n                Resume <ExternalLink className=\"h-3 w-3\" />\n              </Link>\n              <Link\n                href=\"/blog\"\n                className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n              >\n                Latest Posts\n              </Link>\n              <Link\n                href=\"/research\"\n                className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n              >\n                Publications\n              </Link>\n              <Link\n                href=\"/projects\"\n                className=\"text-sm text-muted-foreground hover:text-primary transition-colors\"\n              >\n                Featured Projects\n              </Link>\n            </nav>\n          </div>\n\n          {/* Contact & Social */}\n          <div className=\"space-y-4\">\n            <Typography variant=\"h6\">Connect</Typography>\n            <div className=\"flex flex-col space-y-3\">\n              <div className=\"flex space-x-2\">\n                <Button\n                  href={`${SOCIAL_LINKS.github}/yourusername`}\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  external\n                  aria-label=\"GitHub\"\n                >\n                  <Github className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  href={`${SOCIAL_LINKS.linkedin}/yourlinkedin`}\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  external\n                  aria-label=\"LinkedIn\"\n                >\n                  <Linkedin className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  href={`${SOCIAL_LINKS.twitter}/yourtwitter`}\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  external\n                  aria-label=\"Twitter\"\n                >\n                  <Twitter className=\"h-4 w-4\" />\n                </Button>\n                <Button\n                  href=\"mailto:<EMAIL>\"\n                  variant=\"ghost\"\n                  size=\"icon\"\n                  aria-label=\"Email\"\n                >\n                  <Mail className=\"h-4 w-4\" />\n                </Button>\n              </div>\n              <Button href=\"/contact\" size=\"sm\" className=\"w-fit\">\n                Get in Touch\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\">\n          <Typography variant=\"muted\">\n            © {currentYear} Portfolio. Built with Next.js and Tailwind CSS.\n          </Typography>\n          <div className=\"flex items-center space-x-4\">\n            <Link\n              href=\"/privacy\"\n              className=\"text-xs text-muted-foreground hover:text-primary transition-colors\"\n            >\n              Privacy Policy\n            </Link>\n            <Link\n              href=\"/terms\"\n              className=\"text-xs text-muted-foreground hover:text-primary transition-colors\"\n            >\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,QAAQ,yMAAA,CAAA,SAAM;QACd,UAAU,6MAAA,CAAA,WAAQ;QAClB,SAAS,2MAAA,CAAA,UAAO;QAChB,OAAO,qMAAA,CAAA,OAAI;IACb;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CACvB,cAAA,6LAAC;wCAAK,WAAU;kDAA0F;;;;;;;;;;;8CAI5G,6LAAC,yIAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAQ,WAAU;8CAAW;;;;;;;;;;;;sCAOnD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,aAAU;oCAAC,SAAQ;8CAAK;;;;;;8CACzB,6LAAC;oCAAI,WAAU;8CACZ,0HAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,aAAU;oCAAC,SAAQ;8CAAK;;;;;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;;gDACX;8DACQ,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;sDAEjC,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;sDAGD,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yIAAA,CAAA,aAAU;oCAAC,SAAQ;8CAAK;;;;;;8CACzB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAM,GAAG,0HAAA,CAAA,eAAY,CAAC,MAAM,CAAC,aAAa,CAAC;oDAC3C,SAAQ;oDACR,MAAK;oDACL,QAAQ;oDACR,cAAW;8DAEX,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;8DAEpB,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAM,GAAG,0HAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,aAAa,CAAC;oDAC7C,SAAQ;oDACR,MAAK;oDACL,QAAQ;oDACR,cAAW;8DAEX,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAM,GAAG,0HAAA,CAAA,eAAY,CAAC,OAAO,CAAC,YAAY,CAAC;oDAC3C,SAAQ;oDACR,MAAK;oDACL,QAAQ;oDACR,cAAW;8DAEX,cAAA,6LAAC,2MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAErB,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,cAAW;8DAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGpB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAW,MAAK;4CAAK,WAAU;sDAAQ;;;;;;;;;;;;;;;;;;;;;;;;8BAQ1D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,aAAU;4BAAC,SAAQ;;gCAAQ;gCACvB;gCAAY;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;KAnJgB", "debugId": null}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/analytics/GoogleAnalytics.tsx"], "sourcesContent": ["'use client';\n\nimport Script from 'next/script';\n\ninterface GoogleAnalyticsProps {\n  measurementId: string;\n}\n\nexport function GoogleAnalytics({ measurementId }: GoogleAnalyticsProps) {\n  if (!measurementId || process.env.NODE_ENV !== 'production') {\n    return null;\n  }\n\n  return (\n    <>\n      <Script\n        src={`https://www.googletagmanager.com/gtag/js?id=${measurementId}`}\n        strategy=\"afterInteractive\"\n      />\n      <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n        {`\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${measurementId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        `}\n      </Script>\n    </>\n  );\n}\n\n// Analytics helper functions\nexport const trackEvent = (action: string, category: string, label?: string, value?: number) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', action, {\n      event_category: category,\n      event_label: label,\n      value: value,\n    });\n  }\n};\n\nexport const trackPageView = (url: string, title?: string) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {\n      page_title: title,\n      page_location: url,\n    });\n  }\n};\n\nexport const trackDownload = (filename: string) => {\n  trackEvent('download', 'file', filename);\n};\n\nexport const trackExternalLink = (url: string) => {\n  trackEvent('click', 'external_link', url);\n};\n\nexport const trackFormSubmission = (formName: string) => {\n  trackEvent('submit', 'form', formName);\n};\n"], "names": [], "mappings": ";;;;;;;;AA+C0B;;AA7C1B;AAFA;;;AAQO,SAAS,gBAAgB,EAAE,aAAa,EAAwB;IACrE,wCAA6D;QAC3D,OAAO;IACT;;AAqBF;KAxBgB;AA2BT,MAAM,aAAa,CAAC,QAAgB,UAAkB,OAAgB;IAC3E,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;QAChD,OAAO,IAAI,CAAC,SAAS,QAAQ;YAC3B,gBAAgB;YAChB,aAAa;YACb,OAAO;QACT;IACF;AACF;AAEO,MAAM,gBAAgB,CAAC,KAAa;IACzC,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;QAChD,OAAO,IAAI,CAAC,UAAU,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE;YAC/D,YAAY;YACZ,eAAe;QACjB;IACF;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,WAAW,YAAY,QAAQ;AACjC;AAEO,MAAM,oBAAoB,CAAC;IAChC,WAAW,SAAS,iBAAiB;AACvC;AAEO,MAAM,sBAAsB,CAAC;IAClC,WAAW,UAAU,QAAQ;AAC/B", "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/analytics/WebVitals.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';\n\nfunction sendToAnalytics(metric: any) {\n  // Send to your analytics service\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', metric.name, {\n      event_category: 'Web Vitals',\n      event_label: metric.id,\n      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n      non_interaction: true,\n    });\n  }\n\n  // Also log to console in development\n  if (process.env.NODE_ENV === 'development') {\n    console.log('Web Vital:', metric);\n  }\n}\n\nexport function WebVitals() {\n  useEffect(() => {\n    // Measure Core Web Vitals\n    onCLS(sendToAnalytics);\n    onINP(sendToAnalytics); // INP replaced FID\n    onFCP(sendToAnalytics);\n    onLCP(sendToAnalytics);\n    onTTFB(sendToAnalytics);\n  }, []);\n\n  return null;\n}\n\n// Extend Window interface for gtag\ndeclare global {\n  interface Window {\n    gtag: (...args: any[]) => void;\n  }\n}\n"], "names": [], "mappings": ";;;AAiBM;AAfN;AACA;;AAHA;;;AAKA,SAAS,gBAAgB,MAAW;IAClC,iCAAiC;IACjC,IAAI,aAAkB,eAAe,OAAO,IAAI,EAAE;QAChD,OAAO,IAAI,CAAC,SAAS,OAAO,IAAI,EAAE;YAChC,gBAAgB;YAChB,aAAa,OAAO,EAAE;YACtB,OAAO,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK,QAAQ,OAAO,KAAK,GAAG,OAAO,OAAO,KAAK;YAC5E,iBAAiB;QACnB;IACF;IAEA,qCAAqC;IACrC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,cAAc;IAC5B;AACF;AAEO,SAAS;;IACd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,0BAA0B;YAC1B,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE;YACN,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE,kBAAkB,mBAAmB;YAC3C,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE;YACN,CAAA,GAAA,yJAAA,CAAA,QAAK,AAAD,EAAE;YACN,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE;QACT;8BAAG,EAAE;IAEL,OAAO;AACT;GAXgB;KAAA", "debugId": null}}]}