<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prem Katuwal - Resume</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            color: #2563eb;
            font-size: 2.5em;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .section {
            margin-bottom: 30px;
        }
        .section h2 {
            color: #2563eb;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        .placeholder {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            padding: 40px;
            text-align: center;
            border-radius: 8px;
            margin: 20px 0;
        }
        .placeholder h3 {
            color: #6c757d;
            margin-bottom: 10px;
        }
        .placeholder p {
            color: #6c757d;
            font-style: italic;
        }
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .contact-info span {
            color: #666;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .placeholder { border-style: solid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Prem Katuwal</h1>
        <p><strong>AI Researcher & Software Engineer</strong></p>
        <div class="contact-info">
            <span>📧 <EMAIL></span>
            <span>📍 Chengdu, China</span>
            <span>🔗 GitHub: Katwal-77</span>
            <span>💼 LinkedIn: premkatuwal</span>
        </div>
    </div>

    <div class="placeholder">
        <h3>📄 Resume Placeholder</h3>
        <p>This is a placeholder resume file. Please replace this with Prem Katuwal's actual professional resume.</p>
        <p><strong>Current Status:</strong> Master's student in Computer Science (AI) at UESTC</p>
        <p><strong>Experience:</strong> 2 years of professional software engineering experience</p>
        <p><strong>Education:</strong> University of Electronic Science and Technology of China (UESTC)</p>
        <p><strong>Ranking:</strong> UESTC is ranked 3rd globally in AI according to US News & World Report</p>
    </div>

    <div class="section">
        <h2>🎓 Education</h2>
        <div class="placeholder">
            <p>Add Prem Katuwal's educational background here</p>
        </div>
    </div>

    <div class="section">
        <h2>💼 Professional Experience</h2>
        <div class="placeholder">
            <p>Add Prem Katuwal's 2 years of professional software engineering experience here</p>
        </div>
    </div>

    <div class="section">
        <h2>🔬 Research & Publications</h2>
        <div class="placeholder">
            <p>Add Prem Katuwal's research work and publications here</p>
        </div>
    </div>

    <div class="section">
        <h2>🛠️ Technical Skills</h2>
        <div class="placeholder">
            <p>Add Prem Katuwal's technical skills and technologies here</p>
        </div>
    </div>

    <div class="section">
        <h2>🤝 Volunteering & Community</h2>
        <div class="placeholder">
            <p>Add Prem Katuwal's extensive volunteering background here</p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 40px; color: #666; font-size: 0.9em;">
        <p>📅 Updated: December 2024 | 🔒 Professional Version</p>
        <p><em>To replace this placeholder, convert Prem Katuwal's actual resume to PDF and save as "Prem_Katuwal_Resume.pdf"</em></p>
    </div>
</body>
</html>
