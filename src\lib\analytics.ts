// Advanced Analytics Configuration
import { onCLS, onINP, onFCP, onLCP, onTTFB, type Metric } from 'web-vitals'

// Analytics configuration
export const ANALYTICS_CONFIG = {
  GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '',
  GTAG_ID: process.env.NEXT_PUBLIC_GTAG_ID || '',
  VERCEL_ANALYTICS: process.env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID || '',
  ENABLE_DEBUG: process.env.NODE_ENV === 'development',
} as const

// Custom event types
export interface CustomEvent {
  action: string
  category: string
  label?: string
  value?: number
  custom_parameters?: Record<string, any>
}

// Page view tracking
export const trackPageView = (url: string, title?: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', ANALYTICS_CONFIG.GA_MEASUREMENT_ID, {
      page_title: title,
      page_location: url,
    })
  }
}

// Custom event tracking
export const trackEvent = ({ action, category, label, value, custom_parameters }: CustomEvent) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
      ...custom_parameters,
    })
  }

  // Log in development
  if (ANALYTICS_CONFIG.ENABLE_DEBUG) {
    console.log('📊 Analytics Event:', { action, category, label, value, custom_parameters })
  }
}

// Portfolio-specific event tracking
export const portfolioEvents = {
  // Navigation events
  navigateToSection: (section: string) => {
    trackEvent({
      action: 'navigate_to_section',
      category: 'navigation',
      label: section,
    })
  },

  // Project interactions
  viewProject: (projectId: string, projectTitle: string) => {
    trackEvent({
      action: 'view_project',
      category: 'projects',
      label: projectTitle,
      custom_parameters: { project_id: projectId },
    })
  },

  clickProjectDemo: (projectId: string, projectTitle: string) => {
    trackEvent({
      action: 'click_project_demo',
      category: 'projects',
      label: projectTitle,
      custom_parameters: { project_id: projectId },
    })
  },

  clickProjectGithub: (projectId: string, projectTitle: string) => {
    trackEvent({
      action: 'click_project_github',
      category: 'projects',
      label: projectTitle,
      custom_parameters: { project_id: projectId },
    })
  },

  // Research interactions
  viewPublication: (publicationId: string, publicationTitle: string) => {
    trackEvent({
      action: 'view_publication',
      category: 'research',
      label: publicationTitle,
      custom_parameters: { publication_id: publicationId },
    })
  },

  downloadPaper: (publicationId: string, publicationTitle: string) => {
    trackEvent({
      action: 'download_paper',
      category: 'research',
      label: publicationTitle,
      custom_parameters: { publication_id: publicationId },
    })
  },

  // Blog interactions
  readBlogPost: (postId: string, postTitle: string) => {
    trackEvent({
      action: 'read_blog_post',
      category: 'blog',
      label: postTitle,
      custom_parameters: { post_id: postId },
    })
  },

  shareBlogPost: (postId: string, postTitle: string, platform: string) => {
    trackEvent({
      action: 'share_blog_post',
      category: 'blog',
      label: postTitle,
      custom_parameters: { post_id: postId, platform },
    })
  },

  // Contact interactions
  submitContactForm: (inquiryType: string) => {
    trackEvent({
      action: 'submit_contact_form',
      category: 'contact',
      label: inquiryType,
    })
  },

  downloadResume: () => {
    trackEvent({
      action: 'download_resume',
      category: 'contact',
      label: 'resume_pdf',
    })
  },

  clickSocialLink: (platform: string) => {
    trackEvent({
      action: 'click_social_link',
      category: 'social',
      label: platform,
    })
  },

  // Journey interactions
  viewJourneyMilestone: (milestoneId: string, milestoneTitle: string) => {
    trackEvent({
      action: 'view_journey_milestone',
      category: 'journey',
      label: milestoneTitle,
      custom_parameters: { milestone_id: milestoneId },
    })
  },

  // Testimonial interactions
  viewTestimonial: (testimonialId: string, testimonialAuthor: string) => {
    trackEvent({
      action: 'view_testimonial',
      category: 'testimonials',
      label: testimonialAuthor,
      custom_parameters: { testimonial_id: testimonialId },
    })
  },

  // Search and filter
  searchContent: (query: string, category: string) => {
    trackEvent({
      action: 'search_content',
      category: 'search',
      label: query,
      custom_parameters: { search_category: category },
    })
  },

  filterContent: (filterType: string, filterValue: string) => {
    trackEvent({
      action: 'filter_content',
      category: 'filter',
      label: filterValue,
      custom_parameters: { filter_type: filterType },
    })
  },

  // User engagement
  scrollToSection: (section: string, scrollPercentage: number) => {
    trackEvent({
      action: 'scroll_to_section',
      category: 'engagement',
      label: section,
      value: scrollPercentage,
    })
  },

  timeOnPage: (timeInSeconds: number, page: string) => {
    trackEvent({
      action: 'time_on_page',
      category: 'engagement',
      label: page,
      value: timeInSeconds,
    })
  },
}

// Web Vitals tracking
export const trackWebVitals = () => {
  const vitalsUrl = 'https://vitals.vercel-analytics.com/v1/vitals'

  function sendToAnalytics(metric: Metric) {
    const body = JSON.stringify({
      dsn: ANALYTICS_CONFIG.VERCEL_ANALYTICS,
      id: metric.id,
      page: window.location.pathname,
      href: window.location.href,
      event_name: metric.name,
      value: metric.value.toString(),
      speed: 'unknown',
    })

    if (navigator.sendBeacon) {
      navigator.sendBeacon(vitalsUrl, body)
    } else {
      fetch(vitalsUrl, { body, method: 'POST', keepalive: true })
    }

    // Also send to Google Analytics
    if (window.gtag) {
      window.gtag('event', metric.name, {
        event_category: 'Web Vitals',
        event_label: metric.id,
        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
        non_interaction: true,
      })
    }

    // Log in development
    if (ANALYTICS_CONFIG.ENABLE_DEBUG) {
      console.log('📈 Web Vital:', metric)
    }
  }

  // Track all Core Web Vitals
  onCLS(sendToAnalytics)
  onINP(sendToAnalytics)
  onFCP(sendToAnalytics)
  onLCP(sendToAnalytics)
  onTTFB(sendToAnalytics)
}

// Error tracking
export const trackError = (error: Error, errorInfo?: any) => {
  trackEvent({
    action: 'javascript_error',
    category: 'error',
    label: error.message,
    custom_parameters: {
      error_stack: error.stack,
      error_info: errorInfo,
      user_agent: navigator.userAgent,
      url: window.location.href,
    },
  })

  if (ANALYTICS_CONFIG.ENABLE_DEBUG) {
    console.error('🚨 Error tracked:', error, errorInfo)
  }
}

// User session tracking
export const trackUserSession = () => {
  const sessionStart = Date.now()
  const sessionId = Math.random().toString(36).substring(2, 15)

  // Track session start
  trackEvent({
    action: 'session_start',
    category: 'session',
    custom_parameters: {
      session_id: sessionId,
      timestamp: sessionStart,
      user_agent: navigator.userAgent,
      screen_resolution: `${screen.width}x${screen.height}`,
      viewport_size: `${window.innerWidth}x${window.innerHeight}`,
    },
  })

  // Track session end on page unload
  window.addEventListener('beforeunload', () => {
    const sessionEnd = Date.now()
    const sessionDuration = Math.round((sessionEnd - sessionStart) / 1000)

    trackEvent({
      action: 'session_end',
      category: 'session',
      value: sessionDuration,
      custom_parameters: {
        session_id: sessionId,
        duration_seconds: sessionDuration,
      },
    })
  })

  return sessionId
}

// A/B Testing support
export const trackExperiment = (experimentId: string, variant: string) => {
  trackEvent({
    action: 'experiment_view',
    category: 'experiment',
    label: experimentId,
    custom_parameters: {
      experiment_id: experimentId,
      variant: variant,
    },
  })
}

// Conversion tracking
export const trackConversion = (conversionType: string, value?: number) => {
  trackEvent({
    action: 'conversion',
    category: 'conversion',
    label: conversionType,
    value: value,
  })
}

// Initialize analytics
export const initializeAnalytics = () => {
  if (typeof window !== 'undefined') {
    // Track initial page view
    trackPageView(window.location.href, document.title)
    
    // Start user session tracking
    trackUserSession()
    
    // Initialize Web Vitals tracking
    trackWebVitals()
    
    // Track scroll depth
    let maxScrollDepth = 0
    window.addEventListener('scroll', () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
      )
      
      if (scrollDepth > maxScrollDepth && scrollDepth % 25 === 0) {
        maxScrollDepth = scrollDepth
        trackEvent({
          action: 'scroll_depth',
          category: 'engagement',
          label: `${scrollDepth}%`,
          value: scrollDepth,
        })
      }
    })

    if (ANALYTICS_CONFIG.ENABLE_DEBUG) {
      console.log('📊 Analytics initialized')
    }
  }
}
