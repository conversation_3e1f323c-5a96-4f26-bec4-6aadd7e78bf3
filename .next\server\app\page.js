/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZwYWdlJnBhZ2U9JTJGcGFnZSZhcHBQYXRocz0lMkZwYWdlJnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGcGFnZS50c3gmYXBwRGlyPUMlM0ElNUNVc2VycyU1Q3ByZW1rJTVDRGVza3RvcCU1Q3BmJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDVXNlcnMlNUNwcmVtayU1Q0Rlc2t0b3AlNUNwZiZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsc0JBQXNCLG9KQUF3RjtBQUM5RyxzQkFBc0IsME5BQWdGO0FBQ3RHLHNCQUFzQiwwTkFBZ0Y7QUFDdEcsc0JBQXNCLGdPQUFtRjtBQUN6RyxvQkFBb0IsZ0pBQXNGO0FBR3hHO0FBR0E7QUFDRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUM7QUFDakM7QUFDQTtBQUNBLG9DQUFvQyxzZkFBZ1A7QUFDcFI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLHNmQUFnUDtBQUNwUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUN1QjtBQUdyQjtBQUNGLDZCQUE2QixtQkFBbUI7QUFDaEQ7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUdFO0FBQ0Y7QUFDTyx3QkFBd0IsdUdBQWtCO0FBQ2pEO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IG1vZHVsZTAgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGFwcFxcXFxsYXlvdXQudHN4XCIpO1xuY29uc3QgbW9kdWxlMSA9ICgpID0+IGltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiKTtcbmNvbnN0IG1vZHVsZTIgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9mb3JiaWRkZW4tZXJyb3JcIik7XG5jb25zdCBtb2R1bGUzID0gKCkgPT4gaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvdW5hdXRob3JpemVkLWVycm9yXCIpO1xuY29uc3QgcGFnZTQgPSAoKSA9PiBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbmltcG9ydCB7IEFwcFBhZ2VSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXBhZ2UvbW9kdWxlLmNvbXBpbGVkXCIgd2l0aCB7XG4gICAgJ3R1cmJvcGFjay10cmFuc2l0aW9uJzogJ25leHQtc3NyJ1xufTtcbmltcG9ydCB7IFJvdXRlS2luZCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLWtpbmRcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG4vLyBXZSBpbmplY3QgdGhlIHRyZWUgYW5kIHBhZ2VzIGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCB0cmVlID0ge1xuICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAnJyxcbiAgICAgICAge1xuICAgICAgICBjaGlsZHJlbjogWydfX1BBR0VfXycsIHt9LCB7XG4gICAgICAgICAgcGFnZTogW3BhZ2U0LCBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiXSxcbiAgICAgICAgICBtZXRhZGF0YToge1xuICAgIGljb246IFsoYXN5bmMgKHByb3BzKSA9PiAoYXdhaXQgaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJuZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlcj90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcYXBwXFxcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fXCIpKS5kZWZhdWx0KHByb3BzKSldLFxuICAgIGFwcGxlOiBbXSxcbiAgICBvcGVuR3JhcGg6IFtdLFxuICAgIHR3aXR0ZXI6IFtdLFxuICAgIG1hbmlmZXN0OiB1bmRlZmluZWRcbiAgfVxuICAgICAgICB9XVxuICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAnbGF5b3V0JzogW21vZHVsZTAsIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcYXBwXFxcXGxheW91dC50c3hcIl0sXG4nbm90LWZvdW5kJzogW21vZHVsZTEsIFwibmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL25vdC1mb3VuZC1lcnJvclwiXSxcbidmb3JiaWRkZW4nOiBbbW9kdWxlMiwgXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZm9yYmlkZGVuLWVycm9yXCJdLFxuJ3VuYXV0aG9yaXplZCc6IFttb2R1bGUzLCBcIm5leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy91bmF1dGhvcml6ZWQtZXJyb3JcIl0sXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgaWNvbjogWyhhc3luYyAocHJvcHMpID0+IChhd2FpdCBpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIm5leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyFDOlxcXFxVc2Vyc1xcXFxwcmVta1xcXFxEZXNrdG9wXFxcXHBmXFxcXHNyY1xcXFxhcHBcXFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX19cIikpLmRlZmF1bHQocHJvcHMpKV0sXG4gICAgYXBwbGU6IFtdLFxuICAgIG9wZW5HcmFwaDogW10sXG4gICAgdHdpdHRlcjogW10sXG4gICAgbWFuaWZlc3Q6IHVuZGVmaW5lZFxuICB9XG4gICAgICB9XG4gICAgICBdXG4gICAgICB9LmNoaWxkcmVuO1xuY29uc3QgcGFnZXMgPSBbXCJDOlxcXFxVc2Vyc1xcXFxwcmVta1xcXFxEZXNrdG9wXFxcXHBmXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIl07XG5leHBvcnQgeyB0cmVlLCBwYWdlcyB9O1xuZXhwb3J0IHsgZGVmYXVsdCBhcyBHbG9iYWxFcnJvciB9IGZyb20gXCJuZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvZXJyb3ItYm91bmRhcnlcIiB3aXRoIHtcbiAgICAndHVyYm9wYWNrLXRyYW5zaXRpb24nOiAnbmV4dC1zZXJ2ZXItdXRpbGl0eSdcbn07XG5jb25zdCBfX25leHRfYXBwX3JlcXVpcmVfXyA9IF9fd2VicGFja19yZXF1aXJlX19cbmNvbnN0IF9fbmV4dF9hcHBfbG9hZF9jaHVua19fID0gKCkgPT4gUHJvbWlzZS5yZXNvbHZlKClcbmV4cG9ydCBjb25zdCBfX25leHRfYXBwX18gPSB7XG4gICAgcmVxdWlyZTogX19uZXh0X2FwcF9yZXF1aXJlX18sXG4gICAgbG9hZENodW5rOiBfX25leHRfYXBwX2xvYWRfY2h1bmtfX1xufTtcbmV4cG9ydCAqIGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2FwcC1yZW5kZXIvZW50cnktYmFzZVwiIHdpdGgge1xuICAgICd0dXJib3BhY2stdHJhbnNpdGlvbic6ICduZXh0LXNlcnZlci11dGlsaXR5J1xufTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFBhZ2VSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1BBR0UsXG4gICAgICAgIHBhZ2U6IFwiL3BhZ2VcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6ICcnLFxuICAgICAgICBmaWxlbmFtZTogJycsXG4gICAgICAgIGFwcFBhdGhzOiBbXVxuICAgIH0sXG4gICAgdXNlcmxhbmQ6IHtcbiAgICAgICAgbG9hZGVyVHJlZTogdHJlZVxuICAgIH1cbn0pO1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcGFnZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/GoogleAnalytics.tsx */ \"(rsc)/./src/components/analytics/GoogleAnalytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/WebVitals.tsx */ \"(rsc)/./src/components/analytics/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(rsc)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(rsc)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByZW1rJTVDJTVDRGVza3RvcCU1QyU1Q3BmJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMmRpc3BsYXklNUMlMjIlM0ElNUMlMjJzd2FwJTVDJTIyJTJDJTVDJTIycHJlbG9hZCU1QyUyMiUzQXRydWUlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByZW1rJTVDJTVDRGVza3RvcCU1QyU1Q3BmJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2FuYWx5dGljcyU1QyU1Q0dvb2dsZUFuYWx5dGljcy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJHb29nbGVBbmFseXRpY3MlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcHJlbWslNUMlNUNEZXNrdG9wJTVDJTVDcGYlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDYW5hbHl0aWNzJTVDJTVDV2ViVml0YWxzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMldlYlZpdGFscyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQlNUMlNUNGb290ZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRm9vdGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByZW1rJTVDJTVDRGVza3RvcCU1QyU1Q3BmJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2xheW91dCU1QyU1Q0hlYWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJIZWFkZXIlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUF3SjtBQUN4SjtBQUNBLDRMQUE0STtBQUM1STtBQUNBLGdMQUFtSTtBQUNuSTtBQUNBLGdMQUFtSSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiR29vZ2xlQW5hbHl0aWNzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhbmFseXRpY3NcXFxcR29vZ2xlQW5hbHl0aWNzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiV2ViVml0YWxzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxhbmFseXRpY3NcXFxcV2ViVml0YWxzLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRm9vdGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcRm9vdGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSGVhZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxsYXlvdXRcXFxcSGVhZGVyLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/FeaturedWork.tsx */ \"(rsc)/./src/components/sections/FeaturedWork.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Hero.tsx */ \"(rsc)/./src/components/sections/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Stats.tsx */ \"(rsc)/./src/components/sections/Stats.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByZW1rJTVDJTVDRGVza3RvcCU1QyU1Q3BmJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3NlY3Rpb25zJTVDJTVDRmVhdHVyZWRXb3JrLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkZlYXR1cmVkV29yayUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNzZWN0aW9ucyU1QyU1Q0hlcm8udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVybyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNzZWN0aW9ucyU1QyU1Q1N0YXRzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlN0YXRzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBaUo7QUFDako7QUFDQSxnTEFBaUk7QUFDakk7QUFDQSxrTEFBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkZlYXR1cmVkV29ya1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcRmVhdHVyZWRXb3JrLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSGVyb1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcSGVyby50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlN0YXRzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxzZWN0aW9uc1xcXFxTdGF0cy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJlbWtcXERlc2t0b3BcXHBmXFxzcmNcXGFwcFxcZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"b08ced9c6441\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHByZW1rXFxEZXNrdG9wXFxwZlxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYjA4Y2VkOWM2NDQxXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"preload\":true}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"preload\\\":true}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_layout_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/Header */ \"(rsc)/./src/components/layout/Header.tsx\");\n/* harmony import */ var _components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/Footer */ \"(rsc)/./src/components/layout/Footer.tsx\");\n/* harmony import */ var _components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/seo/StructuredData */ \"(rsc)/./src/components/seo/StructuredData.tsx\");\n/* harmony import */ var _components_analytics_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/analytics/GoogleAnalytics */ \"(rsc)/./src/components/analytics/GoogleAnalytics.tsx\");\n/* harmony import */ var _components_analytics_WebVitals__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/analytics/WebVitals */ \"(rsc)/./src/components/analytics/WebVitals.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/constants */ \"(rsc)/./src/lib/constants.ts\");\n/* harmony import */ var _lib_seo__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/seo */ \"(rsc)/./src/lib/seo.ts\");\n\n\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: {\n        default: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.name,\n        template: `%s | ${_lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.name}`\n    },\n    description: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.description,\n    keywords: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.keywords,\n    authors: [\n        {\n            name: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.author\n        }\n    ],\n    creator: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.author,\n    metadataBase: new URL(_lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.url),\n    openGraph: {\n        type: \"website\",\n        locale: \"en_US\",\n        url: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.url,\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.description,\n        siteName: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.name\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.name,\n        description: _lib_constants__WEBPACK_IMPORTED_MODULE_7__.SITE_CONFIG.description,\n        creator: \"@yourtwitter\"\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__.StructuredData, {\n                        data: (0,_lib_seo__WEBPACK_IMPORTED_MODULE_8__.generatePersonStructuredData)()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seo_StructuredData__WEBPACK_IMPORTED_MODULE_4__.StructuredData, {\n                        data: (0,_lib_seo__WEBPACK_IMPORTED_MODULE_8__.generateWebsiteStructuredData)()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.googleapis.com\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"preconnect\",\n                        href: \"https://fonts.gstatic.com\",\n                        crossOrigin: \"anonymous\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#000000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_preload_true_variableName_inter___WEBPACK_IMPORTED_MODULE_9___default().className)} font-sans antialiased`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_GoogleAnalytics__WEBPACK_IMPORTED_MODULE_5__.GoogleAnalytics, {\n                        measurementId: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || ''\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_WebVitals__WEBPACK_IMPORTED_MODULE_6__.WebVitals, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex min-h-screen flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Header__WEBPACK_IMPORTED_MODULE_2__.Header, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"flex-1\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Footer__WEBPACK_IMPORTED_MODULE_3__.Footer, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 61,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_sections_Hero__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/sections/Hero */ \"(rsc)/./src/components/sections/Hero.tsx\");\n/* harmony import */ var _components_sections_FeaturedWork__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/sections/FeaturedWork */ \"(rsc)/./src/components/sections/FeaturedWork.tsx\");\n/* harmony import */ var _components_sections_Stats__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sections/Stats */ \"(rsc)/./src/components/sections/Stats.tsx\");\n/* harmony import */ var _data_personal_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/data/personal.json */ \"(rsc)/./src/data/personal.json\");\n/* harmony import */ var _data_featured_work_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/featured-work.json */ \"(rsc)/./src/data/featured-work.json\");\n/* harmony import */ var _data_stats_json__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/stats.json */ \"(rsc)/./src/data/stats.json\");\n\n\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Hero__WEBPACK_IMPORTED_MODULE_1__.Hero, {\n                name: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.name,\n                title: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.title,\n                elevatorPitch: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.elevator_pitch,\n                profileImage: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.profile_image,\n                resumeUrl: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.resume_url,\n                social: {\n                    github: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.social.github,\n                    linkedin: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.social.linkedin,\n                    email: _data_personal_json__WEBPACK_IMPORTED_MODULE_4__.email\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_Stats__WEBPACK_IMPORTED_MODULE_3__.Stats, {\n                stats: _data_stats_json__WEBPACK_IMPORTED_MODULE_6__\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sections_FeaturedWork__WEBPACK_IMPORTED_MODULE_2__.FeaturedWork, {\n                items: _data_featured_work_json__WEBPACK_IMPORTED_MODULE_5__.slice(0, 6)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/analytics/GoogleAnalytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/analytics/GoogleAnalytics.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),
/* harmony export */   trackDownload: () => (/* binding */ trackDownload),
/* harmony export */   trackEvent: () => (/* binding */ trackEvent),
/* harmony export */   trackExternalLink: () => (/* binding */ trackExternalLink),
/* harmony export */   trackFormSubmission: () => (/* binding */ trackFormSubmission),
/* harmony export */   trackPageView: () => (/* binding */ trackPageView)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const GoogleAnalytics = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call GoogleAnalytics() from the server but GoogleAnalytics is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"GoogleAnalytics",
);const trackEvent = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackEvent() from the server but trackEvent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackEvent",
);const trackPageView = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackPageView() from the server but trackPageView is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackPageView",
);const trackDownload = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackDownload() from the server but trackDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackDownload",
);const trackExternalLink = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackExternalLink() from the server but trackExternalLink is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackExternalLink",
);const trackFormSubmission = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call trackFormSubmission() from the server but trackFormSubmission is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\GoogleAnalytics.tsx",
"trackFormSubmission",
);

/***/ }),

/***/ "(rsc)/./src/components/analytics/WebVitals.tsx":
/*!************************************************!*\
  !*** ./src/components/analytics/WebVitals.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   WebVitals: () => (/* binding */ WebVitals)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const WebVitals = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call WebVitals() from the server but WebVitals is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\analytics\\WebVitals.tsx",
"WebVitals",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Footer: () => (/* binding */ Footer)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Footer = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Footer.tsx",
"Footer",
);

/***/ }),

/***/ "(rsc)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Header: () => (/* binding */ Header)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Header = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\layout\\Header.tsx",
"Header",
);

/***/ }),

/***/ "(rsc)/./src/components/sections/FeaturedWork.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/FeaturedWork.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   FeaturedWork: () => (/* binding */ FeaturedWork)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const FeaturedWork = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call FeaturedWork() from the server but FeaturedWork is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\FeaturedWork.tsx",
"FeaturedWork",
);

/***/ }),

/***/ "(rsc)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Hero: () => (/* binding */ Hero)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Hero = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Hero.tsx",
"Hero",
);

/***/ }),

/***/ "(rsc)/./src/components/sections/Stats.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/Stats.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Stats: () => (/* binding */ Stats)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const Stats = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Stats() from the server but Stats is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\pf\\src\\components\\sections\\Stats.tsx",
"Stats",
);

/***/ }),

/***/ "(rsc)/./src/components/seo/StructuredData.tsx":
/*!***********************************************!*\
  !*** ./src/components/seo/StructuredData.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StructuredData: () => (/* binding */ StructuredData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction StructuredData({ data }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n        type: \"application/ld+json\",\n        dangerouslySetInnerHTML: {\n            __html: JSON.stringify(data, null, 2)\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\seo\\\\StructuredData.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy9zZW8vU3RydWN0dXJlZERhdGEudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFJTyxTQUFTQSxlQUFlLEVBQUVDLElBQUksRUFBdUI7SUFDMUQscUJBQ0UsOERBQUNDO1FBQ0NDLE1BQUs7UUFDTEMseUJBQXlCO1lBQ3ZCQyxRQUFRQyxLQUFLQyxTQUFTLENBQUNOLE1BQU0sTUFBTTtRQUNyQzs7Ozs7O0FBR04iLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccHJlbWtcXERlc2t0b3BcXHBmXFxzcmNcXGNvbXBvbmVudHNcXHNlb1xcU3RydWN0dXJlZERhdGEudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImludGVyZmFjZSBTdHJ1Y3R1cmVkRGF0YVByb3BzIHtcbiAgZGF0YTogUmVjb3JkPHN0cmluZywgYW55PiB8IEFycmF5PFJlY29yZDxzdHJpbmcsIGFueT4+O1xufVxuXG5leHBvcnQgZnVuY3Rpb24gU3RydWN0dXJlZERhdGEoeyBkYXRhIH06IFN0cnVjdHVyZWREYXRhUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8c2NyaXB0XG4gICAgICB0eXBlPVwiYXBwbGljYXRpb24vbGQranNvblwiXG4gICAgICBkYW5nZXJvdXNseVNldElubmVySFRNTD17e1xuICAgICAgICBfX2h0bWw6IEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpLFxuICAgICAgfX1cbiAgICAvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlN0cnVjdHVyZWREYXRhIiwiZGF0YSIsInNjcmlwdCIsInR5cGUiLCJkYW5nZXJvdXNseVNldElubmVySFRNTCIsIl9faHRtbCIsIkpTT04iLCJzdHJpbmdpZnkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/seo/StructuredData.tsx\n");

/***/ }),

/***/ "(rsc)/./src/data/featured-work.json":
/*!*************************************!*\
  !*** ./src/data/featured-work.json ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"id":"ai-research-framework","title":"Novel AI Research Framework","description":"Developed a groundbreaking framework for improving machine learning model interpretability, resulting in 15% better accuracy across multiple domains.","category":"research","tags":["Machine Learning","AI Interpretability","Deep Learning","Python"],"link":"/research/ai-framework","githubUrl":"https://github.com/yourusername/ai-framework","featured":true},{"id":"distributed-systems-paper","title":"Scalable Distributed Computing","description":"Published research on optimizing distributed systems for large-scale data processing, accepted at top-tier conference with 200+ citations.","category":"publication","tags":["Distributed Systems","Big Data","Performance Optimization"],"link":"/publications/distributed-computing","featured":true},{"id":"open-source-ml-library","title":"MLToolkit - Open Source Library","description":"Created and maintain a popular machine learning library used by 10,000+ developers worldwide, with comprehensive documentation and tutorials.","category":"project","tags":["Open Source","Machine Learning","Python","Community"],"link":"/projects/mltoolkit","githubUrl":"https://github.com/yourusername/mltoolkit","featured":true},{"id":"neural-network-optimization","title":"Neural Network Optimization","description":"Research on novel optimization techniques for deep neural networks, improving training efficiency by 30% while maintaining accuracy.","category":"research","tags":["Neural Networks","Optimization","Deep Learning"],"link":"/research/nn-optimization"},{"id":"data-visualization-platform","title":"Interactive Data Visualization","description":"Built a web-based platform for real-time data visualization and analysis, used by research teams across multiple universities.","category":"project","tags":["Data Visualization","React","D3.js","Real-time"],"link":"/projects/data-viz","githubUrl":"https://github.com/yourusername/data-viz"},{"id":"ai-ethics-publication","title":"AI Ethics in Healthcare","description":"Co-authored paper on ethical considerations in AI-driven healthcare systems, published in leading medical informatics journal.","category":"publication","tags":["AI Ethics","Healthcare","Medical Informatics"],"link":"/publications/ai-ethics-healthcare"}]');

/***/ }),

/***/ "(rsc)/./src/data/personal.json":
/*!********************************!*\
  !*** ./src/data/personal.json ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"name":"Prem Katuwal","title":"AI Researcher & Software Engineer","email":"<EMAIL>","location":"Chengdu, China","bio":"Passionate Master\'s student in Computer Science (AI) at the University of Electronic Science and Technology of China (UESTC), ranked 3rd globally in AI according to US News & World Report. With 2 years of professional software engineering experience and extensive community involvement, I bridge the gap between cutting-edge AI research and practical applications.","elevator_pitch":"I\'m a Master\'s student in AI at UESTC (ranked 3rd globally in AI), with 2 years of professional software engineering experience and extensive volunteering background. I specialize in bridging academic AI research with real-world applications, contributing to both scholarly work and community tech education.","social":{"github":"Katwal-77","linkedin":"premkatuwal","twitter":"premkatuwal","scholar":"prem-katuwal-scholar-id","orcid":"0000-0000-0000-0000","website":"https://premkatuwal.com"},"resume_url":"/resume.pdf","profile_image":"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop&crop=face"}');

/***/ }),

/***/ "(rsc)/./src/data/stats.json":
/*!*****************************!*\
  !*** ./src/data/stats.json ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"id":"publications","value":12,"label":"Publications","description":"Peer-reviewed research papers"},{"id":"citations","value":450,"label":"Citations","suffix":"+","description":"Academic citations received"},{"id":"projects","value":25,"label":"Projects","suffix":"+","description":"Completed technical projects"},{"id":"volunteer-years","value":10,"label":"Years Volunteering","suffix":"+","description":"Community service experience"}]');

/***/ }),

/***/ "(rsc)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_VARIANTS: () => (/* binding */ ANIMATION_VARIANTS),\n/* harmony export */   BLOG_CATEGORIES: () => (/* binding */ BLOG_CATEGORIES),\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   PROJECT_CATEGORIES: () => (/* binding */ PROJECT_CATEGORIES),\n/* harmony export */   PUBLICATION_TYPES: () => (/* binding */ PUBLICATION_TYPES),\n/* harmony export */   SITE_CONFIG: () => (/* binding */ SITE_CONFIG),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_COLORS: () => (/* binding */ THEME_COLORS),\n/* harmony export */   TIMELINE_TYPES: () => (/* binding */ TIMELINE_TYPES)\n/* harmony export */ });\n// Application constants\nconst SITE_CONFIG = {\n    name: 'Prem Katuwal - Portfolio',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n    author: 'Prem Katuwal',\n    keywords: [\n        'Computer Science',\n        'Master\\'s Student',\n        'AI Research',\n        'UESTC',\n        'Machine Learning',\n        'Software Development',\n        'Publications',\n        'Portfolio',\n        'Artificial Intelligence',\n        'Chengdu',\n        'China'\n    ]\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About',\n        href: '/about'\n    },\n    {\n        name: 'Journey',\n        href: '/journey'\n    },\n    {\n        name: 'Research',\n        href: '/research'\n    },\n    {\n        name: 'Projects',\n        href: '/projects'\n    },\n    {\n        name: 'Volunteering',\n        href: '/volunteering'\n    },\n    {\n        name: 'Blog',\n        href: '/blog'\n    },\n    {\n        name: 'Testimonials',\n        href: '/testimonials'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nconst SOCIAL_LINKS = {\n    github: 'https://github.com',\n    linkedin: 'https://linkedin.com/in',\n    twitter: 'https://twitter.com',\n    scholar: 'https://scholar.google.com/citations?user=',\n    orcid: 'https://orcid.org/'\n};\nconst PROJECT_CATEGORIES = {\n    'ai-ml': 'AI & Machine Learning',\n    'web-dev': 'Web Development',\n    'research-tools': 'Research Tools',\n    'open-source': 'Open Source'\n};\nconst PUBLICATION_TYPES = {\n    journal: 'Journal Article',\n    conference: 'Conference Paper',\n    preprint: 'Preprint',\n    thesis: 'Thesis'\n};\nconst BLOG_CATEGORIES = {\n    research: 'Research',\n    technology: 'Technology',\n    career: 'Career',\n    tutorials: 'Tutorials'\n};\nconst TIMELINE_TYPES = {\n    education: 'Education',\n    work: 'Work Experience',\n    research: 'Research',\n    volunteering: 'Volunteering'\n};\nconst CONTACT_INFO = {\n    email: '<EMAIL>',\n    location: 'Chengdu, China',\n    availability: 'Available for research collaborations and opportunities'\n};\nconst ANIMATION_VARIANTS = {\n    fadeIn: {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1\n        }\n    },\n    slideUp: {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideDown: {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideLeft: {\n        hidden: {\n            opacity: 0,\n            x: 20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    slideRight: {\n        hidden: {\n            opacity: 0,\n            x: -20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    scale: {\n        hidden: {\n            opacity: 0,\n            scale: 0.95\n        },\n        visible: {\n            opacity: 1,\n            scale: 1\n        }\n    }\n};\nconst BREAKPOINTS = {\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    '2xl': 1536\n};\nconst THEME_COLORS = {\n    primary: {\n        50: '#eff6ff',\n        100: '#dbeafe',\n        500: '#3b82f6',\n        600: '#2563eb',\n        700: '#1d4ed8',\n        900: '#1e3a8a'\n    },\n    secondary: {\n        50: '#f8fafc',\n        100: '#f1f5f9',\n        500: '#64748b',\n        600: '#475569',\n        700: '#334155',\n        900: '#0f172a'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/seo.ts":
/*!************************!*\
  !*** ./src/lib/seo.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateBlogPostStructuredData: () => (/* binding */ generateBlogPostStructuredData),\n/* harmony export */   generateBreadcrumbStructuredData: () => (/* binding */ generateBreadcrumbStructuredData),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generatePersonStructuredData: () => (/* binding */ generatePersonStructuredData),\n/* harmony export */   generateResearchArticleStructuredData: () => (/* binding */ generateResearchArticleStructuredData),\n/* harmony export */   generateWebsiteStructuredData: () => (/* binding */ generateWebsiteStructuredData),\n/* harmony export */   siteConfig: () => (/* binding */ siteConfig)\n/* harmony export */ });\n// Base SEO configuration\nconst siteConfig = {\n    name: 'Prem Katuwal - AI Researcher & Software Engineer',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC (ranked 3rd globally in AI), with 2 years of professional software engineering experience. Passionate about bridging academia and industry through innovative AI research and community impact.',\n    url: 'https://premkatuwal.com',\n    ogImage: 'https://premkatuwal.com/og-image.jpg',\n    author: {\n        name: 'Prem Katuwal',\n        email: '<EMAIL>',\n        twitter: '@premkatuwal',\n        linkedin: 'https://linkedin.com/in/premkatuwal',\n        github: 'https://github.com/Katwal-77'\n    },\n    keywords: [\n        'AI Research',\n        'Machine Learning',\n        'Software Engineering',\n        'PhD Student',\n        'Computer Science',\n        'Research Publications',\n        'Open Source',\n        'Community Volunteering',\n        'Technology Education',\n        'Academic Research',\n        'Industry Experience',\n        'Full Stack Development',\n        'Data Science',\n        'Artificial Intelligence',\n        'Software Development'\n    ]\n};\n// Generate metadata for pages\nfunction generateMetadata({ title, description, image, url, type = 'website', publishedTime, modifiedTime, keywords = [] }) {\n    const metaTitle = title ? `${title} | ${siteConfig.name}` : siteConfig.name;\n    const metaDescription = description || siteConfig.description;\n    const metaImage = image || siteConfig.ogImage;\n    const metaUrl = url ? `${siteConfig.url}${url}` : siteConfig.url;\n    const allKeywords = [\n        ...siteConfig.keywords,\n        ...keywords\n    ];\n    return {\n        title: metaTitle,\n        description: metaDescription,\n        keywords: allKeywords.join(', '),\n        authors: [\n            {\n                name: siteConfig.author.name,\n                url: siteConfig.url\n            }\n        ],\n        creator: siteConfig.author.name,\n        publisher: siteConfig.author.name,\n        robots: {\n            index: true,\n            follow: true,\n            googleBot: {\n                index: true,\n                follow: true,\n                'max-video-preview': -1,\n                'max-image-preview': 'large',\n                'max-snippet': -1\n            }\n        },\n        openGraph: {\n            type,\n            locale: 'en_US',\n            url: metaUrl,\n            title: metaTitle,\n            description: metaDescription,\n            siteName: siteConfig.name,\n            images: [\n                {\n                    url: metaImage,\n                    width: 1200,\n                    height: 630,\n                    alt: metaTitle\n                }\n            ],\n            ...type === 'article' && {\n                publishedTime,\n                modifiedTime,\n                authors: [\n                    siteConfig.author.name\n                ]\n            }\n        },\n        twitter: {\n            card: 'summary_large_image',\n            title: metaTitle,\n            description: metaDescription,\n            images: [\n                metaImage\n            ],\n            creator: siteConfig.author.twitter,\n            site: siteConfig.author.twitter\n        },\n        alternates: {\n            canonical: metaUrl\n        },\n        other: {\n            'google-site-verification': 'your-google-verification-code'\n        }\n    };\n}\n// Structured data generators\nfunction generatePersonStructuredData() {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'Person',\n        name: siteConfig.author.name,\n        url: siteConfig.url,\n        image: siteConfig.ogImage,\n        sameAs: [\n            siteConfig.author.linkedin,\n            siteConfig.author.github,\n            `https://twitter.com/${siteConfig.author.twitter.replace('@', '')}`\n        ],\n        jobTitle: 'Master\\'s Student & AI Researcher',\n        worksFor: {\n            '@type': 'Organization',\n            name: 'University of Electronic Science and Technology of China (UESTC)'\n        },\n        alumniOf: {\n            '@type': 'Organization',\n            name: 'University of Electronic Science and Technology of China'\n        },\n        knowsAbout: [\n            'Artificial Intelligence',\n            'Machine Learning',\n            'Software Engineering',\n            'Computer Science Research',\n            'Data Science',\n            'Full Stack Development'\n        ],\n        email: siteConfig.author.email,\n        description: siteConfig.description\n    };\n}\nfunction generateWebsiteStructuredData() {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'WebSite',\n        name: siteConfig.name,\n        url: siteConfig.url,\n        description: siteConfig.description,\n        author: {\n            '@type': 'Person',\n            name: siteConfig.author.name\n        },\n        potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n                '@type': 'EntryPoint',\n                urlTemplate: `${siteConfig.url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n        }\n    };\n}\nfunction generateBlogPostStructuredData({ title, description, url, image, publishedAt, updatedAt, readingTime, tags }) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'BlogPosting',\n        headline: title,\n        description,\n        url: `${siteConfig.url}${url}`,\n        image: image || siteConfig.ogImage,\n        datePublished: publishedAt,\n        dateModified: updatedAt || publishedAt,\n        author: {\n            '@type': 'Person',\n            name: siteConfig.author.name,\n            url: siteConfig.url\n        },\n        publisher: {\n            '@type': 'Person',\n            name: siteConfig.author.name,\n            url: siteConfig.url\n        },\n        keywords: tags.join(', '),\n        wordCount: readingTime * 200,\n        timeRequired: `PT${readingTime}M`,\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${siteConfig.url}${url}`\n        }\n    };\n}\nfunction generateResearchArticleStructuredData({ title, abstract, authors, publishedAt, journal, doi, url }) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'ScholarlyArticle',\n        headline: title,\n        abstract,\n        url: `${siteConfig.url}${url}`,\n        datePublished: publishedAt,\n        author: authors.map((author)=>({\n                '@type': 'Person',\n                name: author\n            })),\n        publisher: journal ? {\n            '@type': 'Organization',\n            name: journal\n        } : undefined,\n        identifier: doi ? {\n            '@type': 'PropertyValue',\n            propertyID: 'DOI',\n            value: doi\n        } : undefined,\n        mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${siteConfig.url}${url}`\n        }\n    };\n}\nfunction generateBreadcrumbStructuredData(items) {\n    return {\n        '@context': 'https://schema.org',\n        '@type': 'BreadcrumbList',\n        itemListElement: items.map((item, index)=>({\n                '@type': 'ListItem',\n                position: index + 1,\n                name: item.name,\n                item: `${siteConfig.url}${item.url}`\n            }))\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/seo.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/GoogleAnalytics.tsx */ \"(ssr)/./src/components/analytics/GoogleAnalytics.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/analytics/WebVitals.tsx */ \"(ssr)/./src/components/analytics/WebVitals.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Footer.tsx */ \"(ssr)/./src/components/layout/Footer.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/layout/Header.tsx */ \"(ssr)/./src/components/layout/Header.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22preload%5C%22%3Atrue%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CGoogleAnalytics.tsx%22%2C%22ids%22%3A%5B%22GoogleAnalytics%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Canalytics%5C%5CWebVitals.tsx%22%2C%22ids%22%3A%5B%22WebVitals%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CFooter.tsx%22%2C%22ids%22%3A%5B%22Footer%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Clayout%5C%5CHeader.tsx%22%2C%22ids%22%3A%5B%22Header%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/FeaturedWork.tsx */ \"(ssr)/./src/components/sections/FeaturedWork.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Hero.tsx */ \"(ssr)/./src/components/sections/Hero.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/sections/Stats.tsx */ \"(ssr)/./src/components/sections/Stats.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q3ByZW1rJTVDJTVDRGVza3RvcCU1QyU1Q3BmJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3NlY3Rpb25zJTVDJTVDRmVhdHVyZWRXb3JrLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkZlYXR1cmVkV29yayUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNzZWN0aW9ucyU1QyU1Q0hlcm8udHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIySGVybyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNwcmVtayU1QyU1Q0Rlc2t0b3AlNUMlNUNwZiU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUNzZWN0aW9ucyU1QyU1Q1N0YXRzLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlN0YXRzJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBaUo7QUFDako7QUFDQSxnTEFBaUk7QUFDakk7QUFDQSxrTEFBbUkiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkZlYXR1cmVkV29ya1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcRmVhdHVyZWRXb3JrLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiSGVyb1wiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXHByZW1rXFxcXERlc2t0b3BcXFxccGZcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcc2VjdGlvbnNcXFxcSGVyby50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlN0YXRzXCJdICovIFwiQzpcXFxcVXNlcnNcXFxccHJlbWtcXFxcRGVza3RvcFxcXFxwZlxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFxzZWN0aW9uc1xcXFxTdGF0cy50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CFeaturedWork.tsx%22%2C%22ids%22%3A%5B%22FeaturedWork%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CHero.tsx%22%2C%22ids%22%3A%5B%22Hero%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cpremk%5C%5CDesktop%5C%5Cpf%5C%5Csrc%5C%5Ccomponents%5C%5Csections%5C%5CStats.tsx%22%2C%22ids%22%3A%5B%22Stats%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/GoogleAnalytics.tsx":
/*!******************************************************!*\
  !*** ./src/components/analytics/GoogleAnalytics.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GoogleAnalytics: () => (/* binding */ GoogleAnalytics),\n/* harmony export */   trackDownload: () => (/* binding */ trackDownload),\n/* harmony export */   trackEvent: () => (/* binding */ trackEvent),\n/* harmony export */   trackExternalLink: () => (/* binding */ trackExternalLink),\n/* harmony export */   trackFormSubmission: () => (/* binding */ trackFormSubmission),\n/* harmony export */   trackPageView: () => (/* binding */ trackPageView)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* __next_internal_client_entry_do_not_use__ GoogleAnalytics,trackEvent,trackPageView,trackDownload,trackExternalLink,trackFormSubmission auto */ \n\nfunction GoogleAnalytics({ measurementId }) {\n    if (!measurementId || \"development\" !== 'production') {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                src: `https://www.googletagmanager.com/gtag/js?id=${measurementId}`,\n                strategy: \"afterInteractive\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\analytics\\\\GoogleAnalytics.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics\",\n                strategy: \"afterInteractive\",\n                children: `\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n          gtag('config', '${measurementId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n          });\n        `\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\analytics\\\\GoogleAnalytics.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n// Analytics helper functions\nconst trackEvent = (action, category, label, value)=>{\n    if (false) {}\n};\nconst trackPageView = (url, title)=>{\n    if (false) {}\n};\nconst trackDownload = (filename)=>{\n    trackEvent('download', 'file', filename);\n};\nconst trackExternalLink = (url)=>{\n    trackEvent('click', 'external_link', url);\n};\nconst trackFormSubmission = (formName)=>{\n    trackEvent('submit', 'form', formName);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/GoogleAnalytics.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/analytics/WebVitals.tsx":
/*!************************************************!*\
  !*** ./src/components/analytics/WebVitals.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebVitals: () => (/* binding */ WebVitals)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var web_vitals__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! web-vitals */ \"(ssr)/./node_modules/web-vitals/dist/web-vitals.js\");\n/* __next_internal_client_entry_do_not_use__ WebVitals auto */ \n\nfunction sendToAnalytics(metric) {\n    // Send to your analytics service\n    if (false) {}\n    // Also log to console in development\n    if (true) {\n        console.log('Web Vital:', metric);\n    }\n}\nfunction WebVitals() {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WebVitals.useEffect\": ()=>{\n            // Measure Core Web Vitals\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onCLS)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onINP)(sendToAnalytics); // INP replaced FID\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onFCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onLCP)(sendToAnalytics);\n            (0,web_vitals__WEBPACK_IMPORTED_MODULE_1__.onTTFB)(sendToAnalytics);\n        }\n    }[\"WebVitals.useEffect\"], []);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/analytics/WebVitals.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Footer: () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ExternalLink,Github,Linkedin,Mail,Twitter!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ Footer auto */ \n\n\n\n\n\nfunction Footer() {\n    const currentYear = new Date().getFullYear();\n    const socialIcons = {\n        github: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        linkedin: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        twitter: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        email: _barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"border-t bg-muted/50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\",\n                                        children: \"Portfolio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"muted\",\n                                    className: \"max-w-xs\",\n                                    children: \"Computer Science PhD student passionate about AI research, software development, and knowledge sharing.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Navigation\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: item.href,\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: item.name\n                                        }, item.href, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 41,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Quick Links\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"flex flex-col space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/resume.pdf\",\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1\",\n                                            children: [\n                                                \"Resume \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-3 w-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 24\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/blog\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Latest Posts\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/research\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Publications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                            href: \"/projects\",\n                                            className: \"text-sm text-muted-foreground hover:text-primary transition-colors\",\n                                            children: \"Featured Projects\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                    variant: \"h6\",\n                                    children: \"Connect\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.github}/yourusername`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"GitHub\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 97,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.linkedin}/yourlinkedin`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"LinkedIn\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: `${_lib_constants__WEBPACK_IMPORTED_MODULE_4__.SOCIAL_LINKS.twitter}/yourtwitter`,\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    external: true,\n                                                    \"aria-label\": \"Twitter\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    \"aria-label\": \"Email\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExternalLink_Github_Linkedin_Mail_Twitter_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            href: \"/contact\",\n                                            size: \"sm\",\n                                            className: \"w-fit\",\n                                            children: \"Get in Touch\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 22,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            variant: \"muted\",\n                            children: [\n                                \"\\xa9 \",\n                                currentYear,\n                                \" Portfolio. Built with Next.js and Tailwind CSS.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    className: \"text-xs text-muted-foreground hover:text-primary transition-colors\",\n                                    children: \"Privacy Policy\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/terms\",\n                                    className: \"text-xs text-muted-foreground hover:text-primary transition-colors\",\n                                    children: \"Terms of Service\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Footer.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/layout/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Header: () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _ThemeToggle__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ThemeToggle */ \"(ssr)/./src/components/layout/ThemeToggle.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Header auto */ \n\n\n\n\n\n\n\n\nfunction Header() {\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const toggleMenu = ()=>setIsMenuOpen(!isMenuOpen);\n    const closeMenu = ()=>setIsMenuOpen(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto flex h-16 items-center justify-between px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/\",\n                        className: \"flex items-center space-x-2 font-bold text-xl\",\n                        onClick: closeMenu,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\",\n                            children: \"Portfolio\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"hidden md:flex items-center space-x-6\",\n                        children: _lib_constants__WEBPACK_IMPORTED_MODULE_6__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('text-sm font-medium transition-colors hover:text-primary', pathname === item.href ? 'text-primary' : 'text-muted-foreground'),\n                                children: item.name\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                href: \"/contact\",\n                                size: \"sm\",\n                                children: \"Get in Touch\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 md:hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggle__WEBPACK_IMPORTED_MODULE_5__.ThemeToggle, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                onClick: toggleMenu,\n                                \"aria-label\": \"Toggle menu\",\n                                children: isMenuOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            isMenuOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden border-t bg-background\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                    className: \"container mx-auto px-4 py-4 space-y-4\",\n                    children: [\n                        _lib_constants__WEBPACK_IMPORTED_MODULE_6__.NAVIGATION_ITEMS.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)('block text-sm font-medium transition-colors hover:text-primary', pathname === item.href ? 'text-primary' : 'text-muted-foreground'),\n                                onClick: closeMenu,\n                                children: item.name\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 15\n                            }, this)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-4 border-t\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                href: \"/contact\",\n                                size: \"sm\",\n                                className: \"w-full\",\n                                children: \"Get in Touch\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\Header.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/layout/ThemeToggle.tsx":
/*!***********************************************!*\
  !*** ./src/components/layout/ThemeToggle.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeSelector: () => (/* binding */ ThemeSelector),\n/* harmony export */   ThemeToggle: () => (/* binding */ ThemeToggle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Monitor,Moon,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useTheme */ \"(ssr)/./src/hooks/useTheme.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ ThemeToggle,ThemeSelector auto */ \n\n\n\n\nfunction ThemeToggle({ className, showLabel = false }) {\n    const { theme, resolvedTheme, setTheme } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const themes = [\n        {\n            value: 'light',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: 'Light'\n        },\n        {\n            value: 'dark',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Dark'\n        },\n        {\n            value: 'system',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'System'\n        }\n    ];\n    const currentTheme = themes.find((t)=>t.value === theme) || themes[0];\n    const Icon = currentTheme.icon;\n    const cycleTheme = ()=>{\n        const currentIndex = themes.findIndex((t)=>t.value === theme);\n        const nextIndex = (currentIndex + 1) % themes.length;\n        setTheme(themes[nextIndex].value);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n        variant: \"ghost\",\n        size: \"icon\",\n        onClick: cycleTheme,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('relative', className),\n        \"aria-label\": `Switch to ${themes[(themes.findIndex((t)=>t.value === theme) + 1) % themes.length].label.toLowerCase()} theme`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                className: \"h-5 w-5 transition-all\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            showLabel && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"ml-2 text-sm font-medium\",\n                children: currentTheme.label\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 41,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle theme\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\nfunction ThemeSelector({ className }) {\n    const { theme, setTheme } = (0,_hooks_useTheme__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const themes = [\n        {\n            value: 'light',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            label: 'Light'\n        },\n        {\n            value: 'dark',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            label: 'Dark'\n        },\n        {\n            value: 'system',\n            icon: _barrel_optimize_names_Monitor_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            label: 'System'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center space-x-1', className),\n        children: themes.map(({ value, icon: Icon, label })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                variant: theme === value ? 'secondary' : 'ghost',\n                size: \"sm\",\n                onClick: ()=>setTheme(value),\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, value, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\layout\\\\ThemeToggle.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYXlvdXQvVGhlbWVUb2dnbGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRWtEO0FBQ0Y7QUFDSjtBQUNYO0FBTzFCLFNBQVNNLFlBQVksRUFBRUMsU0FBUyxFQUFFQyxZQUFZLEtBQUssRUFBb0I7SUFDNUUsTUFBTSxFQUFFQyxLQUFLLEVBQUVDLGFBQWEsRUFBRUMsUUFBUSxFQUFFLEdBQUdQLHlEQUFRQTtJQUVuRCxNQUFNUSxTQUFTO1FBQ2I7WUFBRUMsT0FBTztZQUFrQkMsTUFBTWIsNEZBQUdBO1lBQUVjLE9BQU87UUFBUTtRQUNyRDtZQUFFRixPQUFPO1lBQWlCQyxNQUFNZCw0RkFBSUE7WUFBRWUsT0FBTztRQUFPO1FBQ3BEO1lBQUVGLE9BQU87WUFBbUJDLE1BQU1aLDRGQUFPQTtZQUFFYSxPQUFPO1FBQVM7S0FDNUQ7SUFFRCxNQUFNQyxlQUFlSixPQUFPSyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVMLEtBQUssS0FBS0osVUFBVUcsTUFBTSxDQUFDLEVBQUU7SUFDckUsTUFBTU8sT0FBT0gsYUFBYUYsSUFBSTtJQUU5QixNQUFNTSxhQUFhO1FBQ2pCLE1BQU1DLGVBQWVULE9BQU9VLFNBQVMsQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRUwsS0FBSyxLQUFLSjtRQUN2RCxNQUFNYyxZQUFZLENBQUNGLGVBQWUsS0FBS1QsT0FBT1ksTUFBTTtRQUNwRGIsU0FBU0MsTUFBTSxDQUFDVyxVQUFVLENBQUVWLEtBQUs7SUFDbkM7SUFFQSxxQkFDRSw4REFBQ1YseURBQU1BO1FBQ0xzQixTQUFRO1FBQ1JDLE1BQUs7UUFDTEMsU0FBU1A7UUFDVGIsV0FBV0YsOENBQUVBLENBQUMsWUFBWUU7UUFDMUJxQixjQUFZLENBQUMsVUFBVSxFQUFFaEIsTUFBTSxDQUFDLENBQUNBLE9BQU9VLFNBQVMsQ0FBQ0osQ0FBQUEsSUFBS0EsRUFBRUwsS0FBSyxLQUFLSixTQUFTLEtBQUtHLE9BQU9ZLE1BQU0sQ0FBQyxDQUFFVCxLQUFLLENBQUNjLFdBQVcsR0FBRyxNQUFNLENBQUM7OzBCQUU1SCw4REFBQ1Y7Z0JBQUtaLFdBQVU7Ozs7OztZQUNmQywyQkFDQyw4REFBQ3NCO2dCQUFLdkIsV0FBVTswQkFDYlMsYUFBYUQsS0FBSzs7Ozs7OzBCQUd2Qiw4REFBQ2U7Z0JBQUt2QixXQUFVOzBCQUFVOzs7Ozs7Ozs7Ozs7QUFHaEM7QUFFTyxTQUFTd0IsY0FBYyxFQUFFeEIsU0FBUyxFQUEwQjtJQUNqRSxNQUFNLEVBQUVFLEtBQUssRUFBRUUsUUFBUSxFQUFFLEdBQUdQLHlEQUFRQTtJQUVwQyxNQUFNUSxTQUFTO1FBQ2I7WUFBRUMsT0FBTztZQUFrQkMsTUFBTWIsNEZBQUdBO1lBQUVjLE9BQU87UUFBUTtRQUNyRDtZQUFFRixPQUFPO1lBQWlCQyxNQUFNZCw0RkFBSUE7WUFBRWUsT0FBTztRQUFPO1FBQ3BEO1lBQUVGLE9BQU87WUFBbUJDLE1BQU1aLDRGQUFPQTtZQUFFYSxPQUFPO1FBQVM7S0FDNUQ7SUFFRCxxQkFDRSw4REFBQ2lCO1FBQUl6QixXQUFXRiw4Q0FBRUEsQ0FBQywrQkFBK0JFO2tCQUMvQ0ssT0FBT3FCLEdBQUcsQ0FBQyxDQUFDLEVBQUVwQixLQUFLLEVBQUVDLE1BQU1LLElBQUksRUFBRUosS0FBSyxFQUFFLGlCQUN2Qyw4REFBQ1oseURBQU1BO2dCQUVMc0IsU0FBU2hCLFVBQVVJLFFBQVEsY0FBYztnQkFDekNhLE1BQUs7Z0JBQ0xDLFNBQVMsSUFBTWhCLFNBQVNFO2dCQUN4Qk4sV0FBVTs7a0NBRVYsOERBQUNZO3dCQUFLWixXQUFVOzs7Ozs7a0NBQ2hCLDhEQUFDdUI7d0JBQUt2QixXQUFVO2tDQUFXUTs7Ozs7OztlQVB0QkY7Ozs7Ozs7Ozs7QUFZZiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxwcmVta1xcRGVza3RvcFxccGZcXHNyY1xcY29tcG9uZW50c1xcbGF5b3V0XFxUaGVtZVRvZ2dsZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyBNb29uLCBTdW4sIE1vbml0b3IgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL0J1dHRvbic7XG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ0AvaG9va3MvdXNlVGhlbWUnO1xuaW1wb3J0IHsgY24gfSBmcm9tICdAL2xpYi91dGlscyc7XG5cbmludGVyZmFjZSBUaGVtZVRvZ2dsZVByb3BzIHtcbiAgY2xhc3NOYW1lPzogc3RyaW5nO1xuICBzaG93TGFiZWw/OiBib29sZWFuO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVUb2dnbGUoeyBjbGFzc05hbWUsIHNob3dMYWJlbCA9IGZhbHNlIH06IFRoZW1lVG9nZ2xlUHJvcHMpIHtcbiAgY29uc3QgeyB0aGVtZSwgcmVzb2x2ZWRUaGVtZSwgc2V0VGhlbWUgfSA9IHVzZVRoZW1lKCk7XG5cbiAgY29uc3QgdGhlbWVzID0gW1xuICAgIHsgdmFsdWU6ICdsaWdodCcgYXMgY29uc3QsIGljb246IFN1biwgbGFiZWw6ICdMaWdodCcgfSxcbiAgICB7IHZhbHVlOiAnZGFyaycgYXMgY29uc3QsIGljb246IE1vb24sIGxhYmVsOiAnRGFyaycgfSxcbiAgICB7IHZhbHVlOiAnc3lzdGVtJyBhcyBjb25zdCwgaWNvbjogTW9uaXRvciwgbGFiZWw6ICdTeXN0ZW0nIH0sXG4gIF07XG5cbiAgY29uc3QgY3VycmVudFRoZW1lID0gdGhlbWVzLmZpbmQodCA9PiB0LnZhbHVlID09PSB0aGVtZSkgfHwgdGhlbWVzWzBdO1xuICBjb25zdCBJY29uID0gY3VycmVudFRoZW1lLmljb247XG5cbiAgY29uc3QgY3ljbGVUaGVtZSA9ICgpID0+IHtcbiAgICBjb25zdCBjdXJyZW50SW5kZXggPSB0aGVtZXMuZmluZEluZGV4KHQgPT4gdC52YWx1ZSA9PT0gdGhlbWUpO1xuICAgIGNvbnN0IG5leHRJbmRleCA9IChjdXJyZW50SW5kZXggKyAxKSAlIHRoZW1lcy5sZW5ndGg7XG4gICAgc2V0VGhlbWUodGhlbWVzW25leHRJbmRleF0hLnZhbHVlKTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxCdXR0b25cbiAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXG4gICAgICBzaXplPVwiaWNvblwiXG4gICAgICBvbkNsaWNrPXtjeWNsZVRoZW1lfVxuICAgICAgY2xhc3NOYW1lPXtjbigncmVsYXRpdmUnLCBjbGFzc05hbWUpfVxuICAgICAgYXJpYS1sYWJlbD17YFN3aXRjaCB0byAke3RoZW1lc1sodGhlbWVzLmZpbmRJbmRleCh0ID0+IHQudmFsdWUgPT09IHRoZW1lKSArIDEpICUgdGhlbWVzLmxlbmd0aF0hLmxhYmVsLnRvTG93ZXJDYXNlKCl9IHRoZW1lYH1cbiAgICA+XG4gICAgICA8SWNvbiBjbGFzc05hbWU9XCJoLTUgdy01IHRyYW5zaXRpb24tYWxsXCIgLz5cbiAgICAgIHtzaG93TGFiZWwgJiYgKFxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtc20gZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICB7Y3VycmVudFRoZW1lLmxhYmVsfVxuICAgICAgICA8L3NwYW4+XG4gICAgICApfVxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwic3Itb25seVwiPlRvZ2dsZSB0aGVtZTwvc3Bhbj5cbiAgICA8L0J1dHRvbj5cbiAgKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lU2VsZWN0b3IoeyBjbGFzc05hbWUgfTogeyBjbGFzc05hbWU/OiBzdHJpbmcgfSkge1xuICBjb25zdCB7IHRoZW1lLCBzZXRUaGVtZSB9ID0gdXNlVGhlbWUoKTtcblxuICBjb25zdCB0aGVtZXMgPSBbXG4gICAgeyB2YWx1ZTogJ2xpZ2h0JyBhcyBjb25zdCwgaWNvbjogU3VuLCBsYWJlbDogJ0xpZ2h0JyB9LFxuICAgIHsgdmFsdWU6ICdkYXJrJyBhcyBjb25zdCwgaWNvbjogTW9vbiwgbGFiZWw6ICdEYXJrJyB9LFxuICAgIHsgdmFsdWU6ICdzeXN0ZW0nIGFzIGNvbnN0LCBpY29uOiBNb25pdG9yLCBsYWJlbDogJ1N5c3RlbScgfSxcbiAgXTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbignZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xJywgY2xhc3NOYW1lKX0+XG4gICAgICB7dGhlbWVzLm1hcCgoeyB2YWx1ZSwgaWNvbjogSWNvbiwgbGFiZWwgfSkgPT4gKFxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAga2V5PXt2YWx1ZX1cbiAgICAgICAgICB2YXJpYW50PXt0aGVtZSA9PT0gdmFsdWUgPyAnc2Vjb25kYXJ5JyA6ICdnaG9zdCd9XG4gICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRUaGVtZSh2YWx1ZSl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgPlxuICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHNcIj57bGFiZWx9PC9zcGFuPlxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIk1vb24iLCJTdW4iLCJNb25pdG9yIiwiQnV0dG9uIiwidXNlVGhlbWUiLCJjbiIsIlRoZW1lVG9nZ2xlIiwiY2xhc3NOYW1lIiwic2hvd0xhYmVsIiwidGhlbWUiLCJyZXNvbHZlZFRoZW1lIiwic2V0VGhlbWUiLCJ0aGVtZXMiLCJ2YWx1ZSIsImljb24iLCJsYWJlbCIsImN1cnJlbnRUaGVtZSIsImZpbmQiLCJ0IiwiSWNvbiIsImN5Y2xlVGhlbWUiLCJjdXJyZW50SW5kZXgiLCJmaW5kSW5kZXgiLCJuZXh0SW5kZXgiLCJsZW5ndGgiLCJ2YXJpYW50Iiwic2l6ZSIsIm9uQ2xpY2siLCJhcmlhLWxhYmVsIiwidG9Mb3dlckNhc2UiLCJzcGFuIiwiVGhlbWVTZWxlY3RvciIsImRpdiIsIm1hcCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/layout/ThemeToggle.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/FeaturedWork.tsx":
/*!**************************************************!*\
  !*** ./src/components/sections/FeaturedWork.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FeaturedWork: () => (/* binding */ FeaturedWork)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,ExternalLink,Github!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Card */ \"(ssr)/./src/components/ui/Card.tsx\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ FeaturedWork auto */ \n\n\n\n\n\n\nfunction FeaturedWork({ items }) {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    const getCategoryColor = (category)=>{\n        switch(category){\n            case 'research':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n            case 'project':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n            case 'publication':\n                return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n        }\n    };\n    const getCategoryIcon = (category)=>{\n        switch(category){\n            case 'research':\n                return '🔬';\n            case 'project':\n                return '💻';\n            case 'publication':\n                return '📄';\n            default:\n                return '📋';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-muted/30\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    margin: \"-100px\"\n                },\n                variants: containerVariants,\n                className: \"space-y-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.ANIMATION_VARIANTS.slideUp,\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                variant: \"h2\",\n                                className: \"text-3xl lg:text-4xl font-bold\",\n                                children: \"Featured Work\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                variant: \"lead\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"A selection of my most impactful research, projects, and publications that demonstrate my expertise and contributions to the field.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                        children: items.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.ANIMATION_VARIANTS.slideUp,\n                                whileHover: {\n                                    y: -5\n                                },\n                                transition: {\n                                    duration: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"h-full hover:shadow-lg transition-all duration-300 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(item.category)}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"mr-1\",\n                                                                    children: getCategoryIcon(item.category)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                                    lineNumber: 103,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.category.charAt(0).toUpperCase() + item.category.slice(1)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                            lineNumber: 98,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        item.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground\",\n                                                            children: \"⭐ Featured\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                    className: \"group-hover:text-primary transition-colors\",\n                                                    children: item.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                                    className: \"line-clamp-3\",\n                                                    children: item.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-wrap gap-2\",\n                                                    children: [\n                                                        item.tags.slice(0, 3).map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-secondary text-secondary-foreground\",\n                                                                children: tag\n                                                            }, tag, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                                lineNumber: 124,\n                                                                columnNumber: 25\n                                                            }, this)),\n                                                        item.tags.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-secondary text-secondary-foreground\",\n                                                            children: [\n                                                                \"+\",\n                                                                item.tags.length - 3,\n                                                                \" more\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                            lineNumber: 132,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 pt-2\",\n                                                    children: [\n                                                        item.link && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            href: item.link,\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            external: true,\n                                                            className: \"flex-1 group/btn\",\n                                                            children: [\n                                                                \"View Details\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    className: \"ml-2 h-3 w-3 transition-transform group-hover/btn:translate-x-0.5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                                    lineNumber: 149,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                            lineNumber: 141,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        item.githubUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                            href: item.githubUrl,\n                                                            variant: \"ghost\",\n                                                            size: \"icon\",\n                                                            external: true,\n                                                            \"aria-label\": \"View on GitHub\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                            lineNumber: 153,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                                    lineNumber: 139,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 17\n                                }, this)\n                            }, item.id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_4__.ANIMATION_VARIANTS.slideUp,\n                        className: \"text-center pt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            href: \"/projects\",\n                            variant: \"outline\",\n                            size: \"lg\",\n                            className: \"group\",\n                            children: [\n                                \"View All Work\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_ExternalLink_Github_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\FeaturedWork.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/FeaturedWork.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Hero.tsx":
/*!******************************************!*\
  !*** ./src/components/sections/Hero.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Hero: () => (/* binding */ Hero)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=motion!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Download,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/github.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Download,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Download,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Download,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Download,Github,Linkedin,Mail!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _components_ui_Button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/Button */ \"(ssr)/./src/components/ui/Button.tsx\");\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ Hero auto */ \n\n\n\n\n\nfunction Hero({ name, title, elevatorPitch, profileImage, resumeUrl, social }) {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.2,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const socialIcons = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: social?.github ? `https://github.com/${social.github}` : undefined,\n            label: 'GitHub'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: social?.linkedin ? `https://linkedin.com/in/${social.linkedin}` : undefined,\n            label: 'LinkedIn'\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: social?.email ? `mailto:${social.email}` : undefined,\n            label: 'Email'\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"relative min-h-[90vh] flex items-center justify-center overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 -z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-72 h-72 bg-primary/10 rounded-full blur-3xl animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 py-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        animate: \"visible\",\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"large\",\n                                                className: \"text-primary font-medium\",\n                                                children: \"Hello, I'm\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h1\",\n                                                className: \"text-4xl lg:text-6xl font-bold\",\n                                                children: name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h2\",\n                                                className: \"text-2xl lg:text-3xl text-muted-foreground font-normal border-none pb-0\",\n                                                children: title\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            variant: \"lead\",\n                                            className: \"text-lg leading-relaxed max-w-2xl\",\n                                            children: elevatorPitch\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                                        className: \"flex flex-col sm:flex-row gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                href: \"/contact\",\n                                                size: \"lg\",\n                                                className: \"group\",\n                                                children: [\n                                                    \"Get in Touch\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"ml-2 h-4 w-4 transition-transform group-hover:translate-x-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this),\n                                            resumeUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                href: resumeUrl,\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                external: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Download_Github_Linkedin_Mail_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"mr-2 h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \"Download Resume\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                                        className: \"flex items-center space-x-4 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"small\",\n                                                className: \"text-muted-foreground\",\n                                                children: \"Connect with me:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-2\",\n                                                children: socialIcons.map(({ icon: Icon, href, label })=>href && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                        href: href,\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        external: true,\n                                                        className: \"hover:text-primary transition-colors\",\n                                                        \"aria-label\": label,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                            lineNumber: 131,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, label, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideLeft,\n                                className: \"flex justify-center lg:justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"w-80 h-80 lg:w-96 lg:h-96 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 p-2\",\n                                            animate: {\n                                                rotate: [\n                                                    0,\n                                                    360\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 20,\n                                                repeat: Infinity,\n                                                ease: \"linear\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-full h-full rounded-full bg-background flex items-center justify-center overflow-hidden\",\n                                                children: profileImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: profileImage,\n                                                    alt: `${name} - Profile`,\n                                                    className: \"w-full h-full object-cover rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full h-full bg-gradient-to-br from-primary/10 to-accent/10 rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                        variant: \"h1\",\n                                                        className: \"text-6xl text-muted-foreground\",\n                                                        children: name.split(' ').map((n)=>n[0]).join('')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute -top-4 -right-4 w-8 h-8 bg-primary rounded-full\",\n                                            animate: {\n                                                y: [\n                                                    -10,\n                                                    10,\n                                                    -10\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 3,\n                                                repeat: Infinity,\n                                                ease: \"easeInOut\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                                            className: \"absolute -bottom-4 -left-4 w-6 h-6 bg-accent rounded-full\",\n                                            animate: {\n                                                y: [\n                                                    10,\n                                                    -10,\n                                                    10\n                                                ]\n                                            },\n                                            transition: {\n                                                duration: 4,\n                                                repeat: Infinity,\n                                                ease: \"easeInOut\",\n                                                delay: 1\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.fadeIn,\n                        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                            animate: {\n                                y: [\n                                    0,\n                                    10,\n                                    0\n                                ]\n                            },\n                            transition: {\n                                duration: 2,\n                                repeat: Infinity,\n                                ease: \"easeInOut\"\n                            },\n                            className: \"flex flex-col items-center space-y-2 text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    variant: \"small\",\n                                    children: \"Scroll to explore\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-px h-8 bg-current opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Hero.tsx\",\n        lineNumber: 60,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Hero.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/sections/Stats.tsx":
/*!*******************************************!*\
  !*** ./src/components/sections/Stats.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Stats: () => (/* binding */ Stats)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=motion,useInView!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var _barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=motion,useInView!=!framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/Typography */ \"(ssr)/./src/components/ui/Typography.tsx\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/constants */ \"(ssr)/./src/lib/constants.ts\");\n/* __next_internal_client_entry_do_not_use__ Stats auto */ \n\n\n\n\nfunction AnimatedCounter({ value, duration = 2000 }) {\n    const [count, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_4__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AnimatedCounter.useEffect\": ()=>{\n            if (!isInView) return;\n            let startTime;\n            let animationFrame;\n            const animate = {\n                \"AnimatedCounter.useEffect.animate\": (timestamp)=>{\n                    if (!startTime) startTime = timestamp;\n                    const progress = Math.min((timestamp - startTime) / duration, 1);\n                    // Easing function for smooth animation\n                    const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n                    setCount(Math.floor(easeOutQuart * value));\n                    if (progress < 1) {\n                        animationFrame = requestAnimationFrame(animate);\n                    }\n                }\n            }[\"AnimatedCounter.useEffect.animate\"];\n            animationFrame = requestAnimationFrame(animate);\n            return ({\n                \"AnimatedCounter.useEffect\": ()=>{\n                    if (animationFrame) {\n                        cancelAnimationFrame(animationFrame);\n                    }\n                }\n            })[\"AnimatedCounter.useEffect\"];\n        }\n    }[\"AnimatedCounter.useEffect\"], [\n        isInView,\n        value,\n        duration\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        ref: ref,\n        children: count\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n        lineNumber: 60,\n        columnNumber: 10\n    }, this);\n}\nfunction Stats({ stats }) {\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.1\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-20 bg-background\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                initial: \"hidden\",\n                whileInView: \"visible\",\n                viewport: {\n                    once: true,\n                    margin: \"-100px\"\n                },\n                variants: containerVariants,\n                className: \"space-y-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                        className: \"text-center space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"h2\",\n                                className: \"text-3xl lg:text-4xl font-bold\",\n                                children: \"Impact & Achievements\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"lead\",\n                                className: \"max-w-2xl mx-auto\",\n                                children: \"Numbers that reflect my commitment to research excellence, community engagement, and professional growth.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\",\n                        children: stats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                                className: \"text-center space-y-2 group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                        className: \"relative\",\n                                        whileHover: {\n                                            scale: 1.05\n                                        },\n                                        transition: {\n                                            duration: 0.2\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative bg-background border border-border rounded-full w-32 h-32 mx-auto flex items-center justify-center group-hover:border-primary/50 transition-colors duration-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                    variant: \"h2\",\n                                                    className: \"text-2xl lg:text-3xl font-bold text-primary\",\n                                                    children: [\n                                                        stat.prefix,\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnimatedCounter, {\n                                                            value: stat.value\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        stat.suffix\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"h4\",\n                                                className: \"text-lg font-semibold\",\n                                                children: stat.label\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this),\n                                            stat.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                variant: \"muted\",\n                                                className: \"text-sm\",\n                                                children: stat.description\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, stat.id, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_motion_useInView_framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.ANIMATION_VARIANTS.slideUp,\n                        className: \"text-center pt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-4xl mx-auto p-6 bg-muted/50 rounded-lg border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_Typography__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                variant: \"p\",\n                                className: \"text-muted-foreground\",\n                                children: \"These metrics represent my journey in academia and technology, showcasing not just individual achievements but also my commitment to collaborative research, community building, and knowledge sharing. Each number tells a story of dedication, learning, and impact.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\sections\\\\Stats.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/sections/Stats.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/Button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Button,buttonVariants auto */ \n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50', {\n    variants: {\n        variant: {\n            primary: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm',\n            secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm',\n            outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm',\n            ghost: 'hover:bg-accent hover:text-accent-foreground',\n            destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm'\n        },\n        size: {\n            sm: 'h-9 rounded-md px-3 text-xs',\n            md: 'h-10 px-4 py-2',\n            lg: 'h-11 rounded-md px-8 text-base',\n            icon: 'h-10 w-10'\n        }\n    },\n    defaultVariants: {\n        variant: 'primary',\n        size: 'md'\n    }\n});\nconst Button = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, size, href, external, children, ...props }, ref)=>{\n    const Comp = href ? 'a' : 'button';\n    const linkProps = href ? {\n        href,\n        ...external && {\n            target: '_blank',\n            rel: 'noopener noreferrer'\n        }\n    } : {};\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...linkProps,\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Button.tsx\",\n        lineNumber: 58,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = 'Button';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/Card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Card,CardHeader,CardFooter,CardTitle,CardDescription,CardContent auto */ \n\n\n\nconst cardVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200', {\n    variants: {\n        variant: {\n            default: 'border-border',\n            outlined: 'border-2 border-border',\n            elevated: 'shadow-lg hover:shadow-xl'\n        },\n        padding: {\n            none: '',\n            sm: 'p-4',\n            md: 'p-6',\n            lg: 'p-8'\n        }\n    },\n    defaultVariants: {\n        variant: 'default',\n        padding: 'md'\n    }\n});\nconst Card = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, padding, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(cardVariants({\n            variant,\n            padding,\n            className\n        })),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 36,\n        columnNumber: 5\n    }, undefined));\nCard.displayName = 'Card';\nconst CardHeader = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex flex-col space-y-1.5 p-6', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = 'CardHeader';\nconst CardTitle = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-2xl font-semibold leading-none tracking-tight', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = 'CardTitle';\nconst CardDescription = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('text-sm text-muted-foreground', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 79,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = 'CardDescription';\nconst CardContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 92,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = 'CardContent';\nconst CardFooter = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)('flex items-center p-6 pt-0', className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Card.tsx\",\n        lineNumber: 101,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = 'CardFooter';\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Card.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/Typography.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/Typography.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Typography: () => (/* binding */ Typography),\n/* harmony export */   typographyVariants: () => (/* binding */ typographyVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Typography,typographyVariants auto */ \n\n\n\nconst typographyVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)('', {\n    variants: {\n        variant: {\n            h1: 'scroll-m-20 text-4xl font-bold tracking-tight lg:text-6xl xl:text-7xl leading-tight',\n            h2: 'scroll-m-20 border-b pb-3 text-3xl font-semibold tracking-tight first:mt-0 lg:text-4xl leading-tight',\n            h3: 'scroll-m-20 text-2xl font-semibold tracking-tight lg:text-3xl leading-tight',\n            h4: 'scroll-m-20 text-xl font-semibold tracking-tight lg:text-2xl leading-tight',\n            h5: 'scroll-m-20 text-lg font-semibold tracking-tight lg:text-xl leading-tight',\n            h6: 'scroll-m-20 text-base font-semibold tracking-tight lg:text-lg leading-tight',\n            p: 'leading-relaxed text-base lg:text-lg [&:not(:first-child)]:mt-6',\n            lead: 'text-xl lg:text-2xl text-muted-foreground leading-relaxed',\n            large: 'text-lg lg:text-xl font-semibold leading-relaxed',\n            small: 'text-sm font-medium leading-relaxed',\n            muted: 'text-sm lg:text-base text-muted-foreground leading-relaxed',\n            code: 'relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold'\n        }\n    },\n    defaultVariants: {\n        variant: 'p'\n    }\n});\nconst Typography = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(({ className, variant, as, ...props }, ref)=>{\n    const Comp = as || getDefaultElement(variant);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(typographyVariants({\n            variant,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\pf\\\\src\\\\components\\\\ui\\\\Typography.tsx\",\n        lineNumber: 40,\n        columnNumber: 7\n    }, undefined);\n});\nTypography.displayName = 'Typography';\nfunction getDefaultElement(variant) {\n    switch(variant){\n        case 'h1':\n            return 'h1';\n        case 'h2':\n            return 'h2';\n        case 'h3':\n            return 'h3';\n        case 'h4':\n            return 'h4';\n        case 'h5':\n            return 'h5';\n        case 'h6':\n            return 'h6';\n        case 'lead':\n        case 'large':\n        case 'small':\n        case 'muted':\n        case 'p':\n            return 'p';\n        case 'code':\n            return 'code';\n        default:\n            return 'p';\n    }\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/Typography.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useTheme.ts":
/*!*******************************!*\
  !*** ./src/hooks/useTheme.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useTheme auto */ \nfunction useTheme() {\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('light');\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            // Get theme from localStorage or default to system\n            try {\n                const savedTheme = localStorage.getItem('theme');\n                if (savedTheme && [\n                    'light',\n                    'dark',\n                    'system'\n                ].includes(savedTheme)) {\n                    setTheme(savedTheme);\n                }\n            } catch (error) {\n                console.warn('Failed to load theme from localStorage:', error);\n            }\n            setIsLoaded(true);\n        }\n    }[\"useTheme.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            if (!isLoaded) return;\n            const root = window.document.documentElement;\n            // Remove previous theme classes\n            root.classList.remove('light', 'dark');\n            if (theme === 'system') {\n                const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n                root.classList.add(systemTheme);\n                setResolvedTheme(systemTheme);\n            } else {\n                root.classList.add(theme);\n                setResolvedTheme(theme);\n            }\n            // Force a repaint to ensure styles are applied\n            root.style.colorScheme = resolvedTheme;\n        }\n    }[\"useTheme.useEffect\"], [\n        theme,\n        isLoaded\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useTheme.useEffect\": ()=>{\n            if (!isLoaded) return;\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"useTheme.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        const systemTheme = mediaQuery.matches ? 'dark' : 'light';\n                        const root = window.document.documentElement;\n                        root.classList.remove('light', 'dark');\n                        root.classList.add(systemTheme);\n                        setResolvedTheme(systemTheme);\n                        root.style.colorScheme = systemTheme;\n                    }\n                }\n            }[\"useTheme.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"useTheme.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"useTheme.useEffect\"];\n        }\n    }[\"useTheme.useEffect\"], [\n        theme,\n        isLoaded\n    ]);\n    const setThemeAndSave = (newTheme)=>{\n        setTheme(newTheme);\n        try {\n            localStorage.setItem('theme', newTheme);\n        } catch (error) {\n            console.warn('Failed to save theme to localStorage:', error);\n        }\n    };\n    return {\n        theme,\n        resolvedTheme,\n        setTheme: setThemeAndSave,\n        isLoaded,\n        toggleTheme: ()=>{\n            const newTheme = resolvedTheme === 'light' ? 'dark' : 'light';\n            setThemeAndSave(newTheme);\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useTheme.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/constants.ts":
/*!******************************!*\
  !*** ./src/lib/constants.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ANIMATION_VARIANTS: () => (/* binding */ ANIMATION_VARIANTS),\n/* harmony export */   BLOG_CATEGORIES: () => (/* binding */ BLOG_CATEGORIES),\n/* harmony export */   BREAKPOINTS: () => (/* binding */ BREAKPOINTS),\n/* harmony export */   CONTACT_INFO: () => (/* binding */ CONTACT_INFO),\n/* harmony export */   NAVIGATION_ITEMS: () => (/* binding */ NAVIGATION_ITEMS),\n/* harmony export */   PROJECT_CATEGORIES: () => (/* binding */ PROJECT_CATEGORIES),\n/* harmony export */   PUBLICATION_TYPES: () => (/* binding */ PUBLICATION_TYPES),\n/* harmony export */   SITE_CONFIG: () => (/* binding */ SITE_CONFIG),\n/* harmony export */   SOCIAL_LINKS: () => (/* binding */ SOCIAL_LINKS),\n/* harmony export */   THEME_COLORS: () => (/* binding */ THEME_COLORS),\n/* harmony export */   TIMELINE_TYPES: () => (/* binding */ TIMELINE_TYPES)\n/* harmony export */ });\n// Application constants\nconst SITE_CONFIG = {\n    name: 'Prem Katuwal - Portfolio',\n    description: 'Master\\'s student in Computer Science (AI) at UESTC, ranked 3rd globally in AI, showcasing research, projects, and professional experience',\n    url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',\n    author: 'Prem Katuwal',\n    keywords: [\n        'Computer Science',\n        'Master\\'s Student',\n        'AI Research',\n        'UESTC',\n        'Machine Learning',\n        'Software Development',\n        'Publications',\n        'Portfolio',\n        'Artificial Intelligence',\n        'Chengdu',\n        'China'\n    ]\n};\nconst NAVIGATION_ITEMS = [\n    {\n        name: 'Home',\n        href: '/'\n    },\n    {\n        name: 'About',\n        href: '/about'\n    },\n    {\n        name: 'Journey',\n        href: '/journey'\n    },\n    {\n        name: 'Research',\n        href: '/research'\n    },\n    {\n        name: 'Projects',\n        href: '/projects'\n    },\n    {\n        name: 'Volunteering',\n        href: '/volunteering'\n    },\n    {\n        name: 'Blog',\n        href: '/blog'\n    },\n    {\n        name: 'Testimonials',\n        href: '/testimonials'\n    },\n    {\n        name: 'Contact',\n        href: '/contact'\n    }\n];\nconst SOCIAL_LINKS = {\n    github: 'https://github.com',\n    linkedin: 'https://linkedin.com/in',\n    twitter: 'https://twitter.com',\n    scholar: 'https://scholar.google.com/citations?user=',\n    orcid: 'https://orcid.org/'\n};\nconst PROJECT_CATEGORIES = {\n    'ai-ml': 'AI & Machine Learning',\n    'web-dev': 'Web Development',\n    'research-tools': 'Research Tools',\n    'open-source': 'Open Source'\n};\nconst PUBLICATION_TYPES = {\n    journal: 'Journal Article',\n    conference: 'Conference Paper',\n    preprint: 'Preprint',\n    thesis: 'Thesis'\n};\nconst BLOG_CATEGORIES = {\n    research: 'Research',\n    technology: 'Technology',\n    career: 'Career',\n    tutorials: 'Tutorials'\n};\nconst TIMELINE_TYPES = {\n    education: 'Education',\n    work: 'Work Experience',\n    research: 'Research',\n    volunteering: 'Volunteering'\n};\nconst CONTACT_INFO = {\n    email: '<EMAIL>',\n    location: 'Chengdu, China',\n    availability: 'Available for research collaborations and opportunities'\n};\nconst ANIMATION_VARIANTS = {\n    fadeIn: {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1\n        }\n    },\n    slideUp: {\n        hidden: {\n            opacity: 0,\n            y: 20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideDown: {\n        hidden: {\n            opacity: 0,\n            y: -20\n        },\n        visible: {\n            opacity: 1,\n            y: 0\n        }\n    },\n    slideLeft: {\n        hidden: {\n            opacity: 0,\n            x: 20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    slideRight: {\n        hidden: {\n            opacity: 0,\n            x: -20\n        },\n        visible: {\n            opacity: 1,\n            x: 0\n        }\n    },\n    scale: {\n        hidden: {\n            opacity: 0,\n            scale: 0.95\n        },\n        visible: {\n            opacity: 1,\n            scale: 1\n        }\n    }\n};\nconst BREAKPOINTS = {\n    sm: 640,\n    md: 768,\n    lg: 1024,\n    xl: 1280,\n    '2xl': 1536\n};\nconst THEME_COLORS = {\n    primary: {\n        50: '#eff6ff',\n        100: '#dbeafe',\n        500: '#3b82f6',\n        600: '#2563eb',\n        700: '#1d4ed8',\n        900: '#1e3a8a'\n    },\n    secondary: {\n        50: '#f8fafc',\n        100: '#f1f5f9',\n        500: '#64748b',\n        600: '#475569',\n        700: '#334155',\n        900: '#0f172a'\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/constants.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n/**\n * Utility function to merge Tailwind CSS classes with clsx\n */ function cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Format date to readable string\n */ function formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(new Date(date));\n}\n/**\n * Calculate reading time for text content\n */ function calculateReadingTime(text) {\n    const wordsPerMinute = 200;\n    const words = text.trim().split(/\\s+/).length;\n    return Math.ceil(words / wordsPerMinute);\n}\n/**\n * Slugify text for URLs\n */ function slugify(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, '').replace(/[\\s_-]+/g, '-').replace(/^-+|-+$/g, '');\n}\n/**\n * Truncate text to specified length\n */ function truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...';\n}\n/**\n * Debounce function for search and other inputs\n */ function debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\n/**\n * Check if we're in development mode\n */ const isDevelopment = \"development\" === 'development';\n/**\n * Check if we're in production mode\n */ const isProduction = \"development\" === 'production';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/web-vitals","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpremk%5CDesktop%5Cpf&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();