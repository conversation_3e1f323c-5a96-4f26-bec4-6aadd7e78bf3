{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/app/api/health/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\n\n// Health check endpoint for monitoring\nexport async function GET(request: NextRequest) {\n  const startTime = Date.now()\n  \n  try {\n    // Basic health checks\n    const checks = {\n      timestamp: new Date().toISOString(),\n      status: 'healthy',\n      version: process.env.npm_package_version || '1.0.0',\n      environment: process.env.NODE_ENV || 'development',\n      uptime: process.uptime(),\n      memory: process.memoryUsage(),\n      checks: {\n        database: await checkDatabase(),\n        external_apis: await checkExternalAPIs(),\n        file_system: await checkFileSystem(),\n        environment_variables: checkEnvironmentVariables(),\n      }\n    }\n\n    const responseTime = Date.now() - startTime\n    \n    // Determine overall health status\n    const allChecksHealthy = Object.values(checks.checks).every(check => check.status === 'healthy')\n    const overallStatus = allChecksHealthy ? 'healthy' : 'degraded'\n\n    return NextResponse.json({\n      ...checks,\n      status: overallStatus,\n      response_time_ms: responseTime,\n    }, {\n      status: overallStatus === 'healthy' ? 200 : 503,\n      headers: {\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n        'Pragma': 'no-cache',\n        'Expires': '0',\n      },\n    })\n  } catch (error) {\n    const responseTime = Date.now() - startTime\n    \n    return NextResponse.json({\n      timestamp: new Date().toISOString(),\n      status: 'unhealthy',\n      error: error instanceof Error ? error.message : 'Unknown error',\n      response_time_ms: responseTime,\n    }, {\n      status: 500,\n      headers: {\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\n      },\n    })\n  }\n}\n\n// Database health check (placeholder for future database integration)\nasync function checkDatabase() {\n  try {\n    // For now, just check if DATABASE_URL is configured\n    const databaseUrl = process.env.DATABASE_URL\n    \n    if (!databaseUrl) {\n      return {\n        status: 'healthy',\n        message: 'No database configured (static site)',\n        response_time_ms: 0,\n      }\n    }\n\n    // In the future, add actual database connectivity check\n    return {\n      status: 'healthy',\n      message: 'Database connection successful',\n      response_time_ms: 5,\n    }\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      message: error instanceof Error ? error.message : 'Database check failed',\n      response_time_ms: 0,\n    }\n  }\n}\n\n// External APIs health check\nasync function checkExternalAPIs() {\n  const checks = []\n  \n  try {\n    // Check Google Analytics availability\n    if (process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID) {\n      const gaCheck = await checkGoogleAnalytics()\n      checks.push({ service: 'Google Analytics', ...gaCheck })\n    }\n\n    // Check if any external API calls are working\n    const externalApiCheck = await checkExternalServices()\n    checks.push({ service: 'External Services', ...externalApiCheck })\n\n    const allHealthy = checks.every(check => check.status === 'healthy')\n    \n    return {\n      status: allHealthy ? 'healthy' : 'degraded',\n      services: checks,\n      total_services: checks.length,\n    }\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      message: error instanceof Error ? error.message : 'External API check failed',\n      services: checks,\n    }\n  }\n}\n\n// Google Analytics health check\nasync function checkGoogleAnalytics() {\n  try {\n    // Simple check to see if GA is configured\n    const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID\n    \n    if (!gaId) {\n      return {\n        status: 'degraded',\n        message: 'Google Analytics not configured',\n        response_time_ms: 0,\n      }\n    }\n\n    return {\n      status: 'healthy',\n      message: 'Google Analytics configured',\n      measurement_id: gaId.substring(0, 5) + '***',\n      response_time_ms: 1,\n    }\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      message: 'Google Analytics check failed',\n      response_time_ms: 0,\n    }\n  }\n}\n\n// External services check\nasync function checkExternalServices() {\n  try {\n    const startTime = Date.now()\n    \n    // Check if we can make external requests (test with a simple API)\n    const response = await fetch('https://api.github.com/zen', {\n      method: 'GET',\n      headers: { 'User-Agent': 'Portfolio-Health-Check' },\n      signal: AbortSignal.timeout(5000), // 5 second timeout\n    })\n    \n    const responseTime = Date.now() - startTime\n    \n    if (response.ok) {\n      return {\n        status: 'healthy',\n        message: 'External API connectivity working',\n        response_time_ms: responseTime,\n      }\n    } else {\n      return {\n        status: 'degraded',\n        message: `External API returned ${response.status}`,\n        response_time_ms: responseTime,\n      }\n    }\n  } catch (error) {\n    return {\n      status: 'degraded',\n      message: 'External API connectivity issues',\n      response_time_ms: 0,\n    }\n  }\n}\n\n// File system health check\nasync function checkFileSystem() {\n  try {\n    const fs = await import('fs/promises')\n    const path = await import('path')\n    \n    // Check if we can read critical files\n    const criticalFiles = [\n      'package.json',\n      'next.config.js',\n      'tailwind.config.ts',\n    ]\n    \n    const fileChecks = await Promise.all(\n      criticalFiles.map(async (file) => {\n        try {\n          await fs.access(path.join(process.cwd(), file))\n          return { file, status: 'exists' }\n        } catch {\n          return { file, status: 'missing' }\n        }\n      })\n    )\n    \n    const allFilesExist = fileChecks.every(check => check.status === 'exists')\n    \n    return {\n      status: allFilesExist ? 'healthy' : 'degraded',\n      message: allFilesExist ? 'All critical files accessible' : 'Some critical files missing',\n      files: fileChecks,\n    }\n  } catch (error) {\n    return {\n      status: 'unhealthy',\n      message: 'File system check failed',\n      error: error instanceof Error ? error.message : 'Unknown error',\n    }\n  }\n}\n\n// Environment variables check\nfunction checkEnvironmentVariables() {\n  const requiredEnvVars = [\n    'NODE_ENV',\n    'NEXT_PUBLIC_SITE_URL',\n  ]\n  \n  const optionalEnvVars = [\n    'NEXT_PUBLIC_GA_MEASUREMENT_ID',\n    'CONTACT_EMAIL',\n    'SMTP_HOST',\n  ]\n  \n  const envChecks = {\n    required: requiredEnvVars.map(envVar => ({\n      variable: envVar,\n      status: process.env[envVar] ? 'set' : 'missing',\n      value: process.env[envVar] ? '***' : undefined,\n    })),\n    optional: optionalEnvVars.map(envVar => ({\n      variable: envVar,\n      status: process.env[envVar] ? 'set' : 'not_set',\n      value: process.env[envVar] ? '***' : undefined,\n    })),\n  }\n  \n  const allRequiredSet = envChecks.required.every(check => check.status === 'set')\n  \n  return {\n    status: allRequiredSet ? 'healthy' : 'degraded',\n    message: allRequiredSet ? 'All required environment variables set' : 'Some required environment variables missing',\n    variables: envChecks,\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAGO,eAAe,IAAI,OAAoB;IAC5C,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,sBAAsB;QACtB,MAAM,SAAS;YACb,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;YAC5C,aAAa,mDAAwB;YACrC,QAAQ,QAAQ,MAAM;YACtB,QAAQ,QAAQ,WAAW;YAC3B,QAAQ;gBACN,UAAU,MAAM;gBAChB,eAAe,MAAM;gBACrB,aAAa,MAAM;gBACnB,uBAAuB;YACzB;QACF;QAEA,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,kCAAkC;QAClC,MAAM,mBAAmB,OAAO,MAAM,CAAC,OAAO,MAAM,EAAE,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACtF,MAAM,gBAAgB,mBAAmB,YAAY;QAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,GAAG,MAAM;YACT,QAAQ;YACR,kBAAkB;QACpB,GAAG;YACD,QAAQ,kBAAkB,YAAY,MAAM;YAC5C,SAAS;gBACP,iBAAiB;gBACjB,UAAU;gBACV,WAAW;YACb;QACF;IACF,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW,IAAI,OAAO,WAAW;YACjC,QAAQ;YACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAChD,kBAAkB;QACpB,GAAG;YACD,QAAQ;YACR,SAAS;gBACP,iBAAiB;YACnB;QACF;IACF;AACF;AAEA,sEAAsE;AACtE,eAAe;IACb,IAAI;QACF,oDAAoD;QACpD,MAAM,cAAc,QAAQ,GAAG,CAAC,YAAY;QAE5C,IAAI,CAAC,aAAa;YAChB,OAAO;gBACL,QAAQ;gBACR,SAAS;gBACT,kBAAkB;YACpB;QACF;QAEA,wDAAwD;QACxD,OAAO;YACL,QAAQ;YACR,SAAS;YACT,kBAAkB;QACpB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,kBAAkB;QACpB;IACF;AACF;AAEA,6BAA6B;AAC7B,eAAe;IACb,MAAM,SAAS,EAAE;IAEjB,IAAI;QACF,sCAAsC;QACtC,IAAI,QAAQ,GAAG,CAAC,6BAA6B,EAAE;YAC7C,MAAM,UAAU,MAAM;YACtB,OAAO,IAAI,CAAC;gBAAE,SAAS;gBAAoB,GAAG,OAAO;YAAC;QACxD;QAEA,8CAA8C;QAC9C,MAAM,mBAAmB,MAAM;QAC/B,OAAO,IAAI,CAAC;YAAE,SAAS;YAAqB,GAAG,gBAAgB;QAAC;QAEhE,MAAM,aAAa,OAAO,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAE1D,OAAO;YACL,QAAQ,aAAa,YAAY;YACjC,UAAU;YACV,gBAAgB,OAAO,MAAM;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD,UAAU;QACZ;IACF;AACF;AAEA,gCAAgC;AAChC,eAAe;IACb,IAAI;QACF,0CAA0C;QAC1C,MAAM,OAAO,QAAQ,GAAG,CAAC,6BAA6B;QAEtD,IAAI,CAAC,MAAM;YACT,OAAO;gBACL,QAAQ;gBACR,SAAS;gBACT,kBAAkB;YACpB;QACF;QAEA,OAAO;YACL,QAAQ;YACR,SAAS;YACT,gBAAgB,KAAK,SAAS,CAAC,GAAG,KAAK;YACvC,kBAAkB;QACpB;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,SAAS;YACT,kBAAkB;QACpB;IACF;AACF;AAEA,0BAA0B;AAC1B,eAAe;IACb,IAAI;QACF,MAAM,YAAY,KAAK,GAAG;QAE1B,kEAAkE;QAClE,MAAM,WAAW,MAAM,MAAM,8BAA8B;YACzD,QAAQ;YACR,SAAS;gBAAE,cAAc;YAAyB;YAClD,QAAQ,YAAY,OAAO,CAAC;QAC9B;QAEA,MAAM,eAAe,KAAK,GAAG,KAAK;QAElC,IAAI,SAAS,EAAE,EAAE;YACf,OAAO;gBACL,QAAQ;gBACR,SAAS;gBACT,kBAAkB;YACpB;QACF,OAAO;YACL,OAAO;gBACL,QAAQ;gBACR,SAAS,CAAC,sBAAsB,EAAE,SAAS,MAAM,EAAE;gBACnD,kBAAkB;YACpB;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,SAAS;YACT,kBAAkB;QACpB;IACF;AACF;AAEA,2BAA2B;AAC3B,eAAe;IACb,IAAI;QACF,MAAM,KAAK;QACX,MAAM,OAAO;QAEb,sCAAsC;QACtC,MAAM,gBAAgB;YACpB;YACA;YACA;SACD;QAED,MAAM,aAAa,MAAM,QAAQ,GAAG,CAClC,cAAc,GAAG,CAAC,OAAO;YACvB,IAAI;gBACF,MAAM,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,QAAQ,GAAG,IAAI;gBACzC,OAAO;oBAAE;oBAAM,QAAQ;gBAAS;YAClC,EAAE,OAAM;gBACN,OAAO;oBAAE;oBAAM,QAAQ;gBAAU;YACnC;QACF;QAGF,MAAM,gBAAgB,WAAW,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QAEjE,OAAO;YACL,QAAQ,gBAAgB,YAAY;YACpC,SAAS,gBAAgB,kCAAkC;YAC3D,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,QAAQ;YACR,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;IACF;AACF;AAEA,8BAA8B;AAC9B,SAAS;IACP,MAAM,kBAAkB;QACtB;QACA;KACD;IAED,MAAM,kBAAkB;QACtB;QACA;QACA;KACD;IAED,MAAM,YAAY;QAChB,UAAU,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC;gBACvC,UAAU;gBACV,QAAQ,QAAQ,GAAG,CAAC,OAAO,GAAG,QAAQ;gBACtC,OAAO,QAAQ,GAAG,CAAC,OAAO,GAAG,QAAQ;YACvC,CAAC;QACD,UAAU,gBAAgB,GAAG,CAAC,CAAA,SAAU,CAAC;gBACvC,UAAU;gBACV,QAAQ,QAAQ,GAAG,CAAC,OAAO,GAAG,QAAQ;gBACtC,OAAO,QAAQ,GAAG,CAAC,OAAO,GAAG,QAAQ;YACvC,CAAC;IACH;IAEA,MAAM,iBAAiB,UAAU,QAAQ,CAAC,KAAK,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;IAE1E,OAAO;QACL,QAAQ,iBAAiB,YAAY;QACrC,SAAS,iBAAiB,2CAA2C;QACrE,WAAW;IACb;AACF", "debugId": null}}]}