'use client';

import { useState, useEffect } from 'react';
import { Moon, Sun, Monitor } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useTheme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';

interface ThemeToggleProps {
  className?: string;
  showLabel?: boolean;
}

export function ThemeToggle({ className, showLabel = false }: ThemeToggleProps) {
  const { theme, resolvedTheme, setTheme, isLoaded } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted || !isLoaded) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className={cn('relative', className)}
        disabled
      >
        <Monitor className="h-5 w-5" />
        <span className="sr-only">Loading theme...</span>
      </Button>
    );
  }

  const themes = [
    { value: 'light' as const, icon: Sun, label: 'Light' },
    { value: 'dark' as const, icon: Moon, label: 'Dark' },
    { value: 'system' as const, icon: Monitor, label: 'System' },
  ];

  const currentTheme = themes.find(t => t.value === theme) || themes[0];
  const Icon = currentTheme.icon;

  const cycleTheme = () => {
    const currentIndex = themes.findIndex(t => t.value === theme);
    const nextIndex = (currentIndex + 1) % themes.length;
    setTheme(themes[nextIndex]!.value);
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={cycleTheme}
      className={cn('relative', className)}
      aria-label={`Switch to ${themes[(themes.findIndex(t => t.value === theme) + 1) % themes.length]!.label.toLowerCase()} theme`}
    >
      <Icon className="h-5 w-5 transition-all" />
      {showLabel && (
        <span className="ml-2 text-sm font-medium">
          {currentTheme.label}
        </span>
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
}

export function ThemeSelector({ className }: { className?: string }) {
  const { theme, setTheme, isLoaded } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const themes = [
    { value: 'light' as const, icon: Sun, label: 'Light' },
    { value: 'dark' as const, icon: Moon, label: 'Dark' },
    { value: 'system' as const, icon: Monitor, label: 'System' },
  ];

  // Don't render until mounted to prevent hydration mismatch
  if (!mounted || !isLoaded) {
    return (
      <div className={cn('flex items-center space-x-1', className)}>
        {themes.map(({ value, icon: Icon, label }) => (
          <Button
            key={value}
            variant="ghost"
            size="sm"
            disabled
            className="flex items-center space-x-2"
          >
            <Icon className="h-4 w-4" />
            <span className="text-xs">{label}</span>
          </Button>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {themes.map(({ value, icon: Icon, label }) => (
        <Button
          key={value}
          variant={theme === value ? 'secondary' : 'ghost'}
          size="sm"
          onClick={() => setTheme(value)}
          className="flex items-center space-x-2"
        >
          <Icon className="h-4 w-4" />
          <span className="text-xs">{label}</span>
        </Button>
      ))}
    </div>
  );
}
