# Resume Directory

This directory contains <PERSON><PERSON>'s professional resume files.

## Files

- `Prem_Katuwal_Resume.pdf` - Main professional resume (PDF format)
- `Prem_Katuwal_CV.pdf` - Academic CV (PDF format) - Optional
- `README.md` - This file

## Instructions

1. **Replace the placeholder PDF** with your actual resume
2. **Keep the filename** as `Prem_Katuwal_Resume.pdf` for the download links to work
3. **Update the date** in the resume section when you upload a new version

## Resume Content Suggestions

Your resume should highlight:

### 🎓 Education
- **Master's in Computer Science (AI)** at UESTC
- **Global Ranking**: 3rd in AI (US News & World Report)
- **GPA/Achievements**: Academic performance
- **Relevant Coursework**: AI, Machine Learning, etc.

### 💼 Professional Experience
- **2+ years software engineering experience**
- **Specific roles and companies**
- **Key achievements and metrics**
- **Technologies used**

### 🔬 Research & Publications
- **Research projects at UESTC**
- **Publications and papers**
- **Conference presentations**
- **Research collaborations**

### 🤝 Volunteering & Community Impact
- **UN Enumerator experience**
- **Community tech training (300+ people)**
- **Mentoring and teaching**
- **Open source contributions**

### 🛠️ Technical Skills
- **Programming Languages**: Python, JavaScript, TypeScript, etc.
- **AI/ML Frameworks**: TensorFlow, PyTorch, etc.
- **Web Technologies**: React, Next.js, Node.js, etc.
- **Tools & Platforms**: Git, Docker, AWS, etc.

### 🏆 Achievements & Certifications
- **Academic honors**
- **Professional certifications**
- **Awards and recognition**
- **Notable projects**

## File Format Requirements

- **Format**: PDF (recommended)
- **Size**: Under 2MB for web optimization
- **Pages**: 1-2 pages (1 page preferred for software roles)
- **Font**: Professional fonts (Arial, Calibri, Times New Roman)
- **Layout**: Clean, ATS-friendly format

## Privacy Note

Make sure your resume doesn't contain sensitive information like:
- Full address (city/country is sufficient)
- Personal ID numbers
- Private phone numbers (use professional contact only)

## Update Process

1. Replace the PDF file in this directory
2. Update the "Updated" date in the ResumeSection component
3. Test the download functionality
4. Commit and deploy changes

---

**Note**: Currently contains a placeholder. Replace with your actual professional resume.
