# 🚀 Quick Reference Commands

Essential commands for developing, testing, and deploying Pre<PERSON>'s portfolio.

## 🏃‍♂️ Quick Start

```bash
# Clone and setup
git clone https://github.com/Katwal-77/pf.git
cd pf
npm install
cp .env.example .env.local

# Start development
npm run dev
# → http://localhost:3002
```

## 🛠️ Development Commands

### Core Development
```bash
npm run dev          # Start development server (port 3002)
npm run build        # Build for production
npm run start        # Start production server
npm run type-check   # TypeScript type checking
```

### Code Quality
```bash
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues automatically
npm run format       # Format code with Prettier
npm run format:check # Check code formatting
```

### Testing
```bash
npm test                    # Run all tests
npm run test:watch          # Run tests in watch mode
npm run test:coverage       # Generate coverage report
npm run test:ci             # Run tests for CI/CD
npm run test:accessibility  # Run accessibility tests
npm run test:performance    # Run performance tests
npm run test:integration    # Run integration tests
```

## 🚀 Deployment Commands

### Pre-deployment
```bash
npm run deploy:check    # Pre-deployment validation
npm run analyze         # Analyze bundle size
npm run lighthouse      # Run Lighthouse audit
```

### Deploy
```bash
npm run deploy          # Deploy to production (Vercel)
npm run deploy:preview  # Deploy preview version
vercel --prod          # Direct Vercel deployment
```

### Monitoring
```bash
# Health checks
curl http://localhost:3002/api/health
curl https://premkatuwal.com/api/health

# Analytics
curl http://localhost:3002/api/analytics
curl https://premkatuwal.com/api/analytics?metric=web_vitals

# Deployment info
curl http://localhost:3002/api/deployment
```

## 🔧 Utility Commands

### Package Management
```bash
npm install              # Install dependencies
npm update              # Update dependencies
npm audit               # Security audit
npm audit fix           # Fix security issues
```

### Git Workflow
```bash
git add .
git commit -m "message"
git push origin main

# Pre-commit checks (automatic)
# - ESLint
# - Prettier
# - TypeScript
# - Tests
```

### Environment
```bash
cp .env.example .env.local    # Copy environment template
nano .env.local               # Edit environment (Linux/macOS)
notepad .env.local           # Edit environment (Windows)
```

## 🐛 Troubleshooting Commands

### Clear Cache
```bash
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### Port Issues
```bash
npx kill-port 3002          # Kill process on port 3002
npm run dev -- --port 3003  # Use different port
```

### Performance Analysis
```bash
npm run analyze              # Bundle analysis
npm run lighthouse           # Performance audit
npm run test:performance     # Performance tests
```

## 📊 Monitoring Commands

### Real-time Monitoring
```bash
# Application health
watch -n 5 'curl -s http://localhost:3002/api/health | jq'

# Performance metrics
watch -n 10 'curl -s http://localhost:3002/api/analytics?metric=web_vitals | jq'

# Server logs
npm run dev 2>&1 | tee development.log
```

### Production Monitoring
```bash
# Health check
curl -f https://premkatuwal.com/api/health || echo "Site down!"

# Performance check
curl -s https://premkatuwal.com/api/analytics?metric=web_vitals | jq '.data.web_vitals'

# Uptime monitoring (with external service)
curl -X POST "https://api.uptimerobot.com/v2/getMonitors" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "api_key=YOUR_API_KEY&format=json"
```

## 🔍 Debug Commands

### Development Debugging
```bash
# Debug build
npm run build 2>&1 | tee build.log

# Debug tests
npm test -- --verbose

# Debug TypeScript
npx tsc --noEmit --listFiles

# Debug ESLint
npx eslint . --debug
```

### Production Debugging
```bash
# Check deployment
vercel ls

# View deployment logs
vercel logs

# Check environment variables
vercel env ls
```

## 📈 Analytics Commands

### Local Analytics
```bash
# Get all analytics
curl http://localhost:3002/api/analytics | jq

# Get specific metrics
curl "http://localhost:3002/api/analytics?metric=overview&timeframe=7d" | jq

# Get traffic sources
curl "http://localhost:3002/api/analytics?metric=traffic_sources" | jq

# Get top pages
curl "http://localhost:3002/api/analytics?metric=top_pages" | jq
```

### Production Analytics
```bash
# Production analytics
curl https://premkatuwal.com/api/analytics | jq

# Web vitals
curl "https://premkatuwal.com/api/analytics?metric=web_vitals" | jq

# Generate report
curl -X PUT https://premkatuwal.com/api/analytics \
  -H "Content-Type: application/json" \
  -d '{"action":"generate_report"}' | jq
```

## 🛡️ Security Commands

### Security Audit
```bash
npm audit                    # Check for vulnerabilities
npm audit fix               # Fix vulnerabilities
npm audit --audit-level=high # Check high-severity only
```

### Dependency Check
```bash
npx depcheck                # Check unused dependencies
npx npm-check-updates       # Check for updates
npx license-checker         # Check licenses
```

## 🎯 One-liner Commands

### Quick Setup
```bash
git clone https://github.com/Katwal-77/pf.git && cd pf && npm install && cp .env.example .env.local && npm run dev
```

### Full Quality Check
```bash
npm run lint && npm run type-check && npm run format:check && npm test && npm run build
```

### Deploy with Checks
```bash
npm run deploy:check && npm run deploy
```

### Monitor Production
```bash
curl -f https://premkatuwal.com/api/health && echo "✅ Site is healthy"
```

## 📱 Mobile Testing Commands

### Local Mobile Testing
```bash
# Get local IP for mobile testing
ipconfig getifaddr en0  # macOS
hostname -I | awk '{print $1}'  # Linux
ipconfig | findstr IPv4  # Windows

# Then visit http://YOUR_IP:3002 on mobile
```

### Lighthouse Mobile Audit
```bash
npx lighthouse http://localhost:3002 \
  --preset=perf \
  --emulated-form-factor=mobile \
  --throttling-method=simulate \
  --output=html \
  --output-path=./lighthouse-mobile.html
```

## 🔄 Backup Commands

### Backup Configuration
```bash
# Backup important files
tar -czf portfolio-backup-$(date +%Y%m%d).tar.gz \
  .env.local \
  src/data/ \
  public/ \
  package.json \
  README.md \
  DEPLOYMENT.md
```

### Restore from Backup
```bash
# Extract backup
tar -xzf portfolio-backup-YYYYMMDD.tar.gz

# Restore dependencies
npm install
```

---

**💡 Pro Tip**: Bookmark this file for quick access to all commands!

**🔗 Quick Links**:
- [Setup Guide](./SETUP.md)
- [Deployment Guide](./DEPLOYMENT.md)
- [Main README](./README.md)
