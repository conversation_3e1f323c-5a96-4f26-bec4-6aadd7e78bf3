'use client'

import { useState } from 'react'
import { Download, FileText, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { portfolioEvents } from '@/lib/analytics'

interface ResumeDownloadProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showIcon?: boolean
  className?: string
}

export function ResumeDownload({ 
  variant = 'default', 
  size = 'default',
  showIcon = true,
  className 
}: ResumeDownloadProps) {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = async () => {
    try {
      setIsDownloading(true)
      
      // Track download event
      portfolioEvents.downloadResume()
      
      // Create download link
      const link = document.createElement('a')
      link.href = '/resume/Prem_Katuwal_Resume.pdf'
      link.download = 'Prem_Katuwal_Resume.pdf'
      link.target = '_blank'
      
      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Show success message (optional)
      console.log('Resume download initiated')
      
    } catch (error) {
      console.error('Error downloading resume:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleDownload}
      disabled={isDownloading}
      className={className}
    >
      {showIcon && (
        isDownloading ? (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        ) : (
          <Download className="mr-2 h-4 w-4" />
        )
      )}
      {isDownloading ? 'Downloading...' : 'Download Resume'}
    </Button>
  )
}

// Resume preview component
export function ResumePreview() {
  const [isLoading, setIsLoading] = useState(false)

  const handlePreview = () => {
    setIsLoading(true)
    
    // Track preview event
    portfolioEvents.trackEvent({
      action: 'preview_resume',
      category: 'resume',
      label: 'pdf_preview'
    })
    
    // Open resume in new tab
    window.open('/resume/Prem_Katuwal_Resume.pdf', '_blank')
    setIsLoading(false)
  }

  return (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={handlePreview}
        disabled={isLoading}
      >
        <FileText className="mr-2 h-4 w-4" />
        Preview
      </Button>
      <ResumeDownload variant="default" size="sm" />
    </div>
  )
}

// Resume section for about page or contact page
export function ResumeSection() {
  return (
    <div className="rounded-lg border bg-card p-6 space-y-4">
      <div className="flex items-center space-x-3">
        <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10">
          <FileText className="h-6 w-6 text-primary" />
        </div>
        <div>
          <h3 className="font-semibold text-lg">Resume</h3>
          <p className="text-sm text-muted-foreground">
            Download my complete professional resume
          </p>
        </div>
      </div>
      
      <div className="space-y-3">
        <p className="text-sm text-muted-foreground">
          Get a comprehensive overview of my education at UESTC (ranked 3rd globally in AI), 
          professional software engineering experience, research contributions, and community impact.
        </p>
        
        <div className="flex items-center space-x-3">
          <ResumeDownload />
          <Button
            variant="outline"
            size="default"
            onClick={() => window.open('/resume/Prem_Katuwal_Resume.pdf', '_blank')}
          >
            <ExternalLink className="mr-2 h-4 w-4" />
            View Online
          </Button>
        </div>
        
        <div className="text-xs text-muted-foreground">
          <p>📄 PDF Format • 📊 Updated December 2024 • 🔒 Professional Version</p>
        </div>
      </div>
    </div>
  )
}
