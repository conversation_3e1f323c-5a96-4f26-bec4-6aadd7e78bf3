import { BlogPageClient } from './BlogPageClient';

// Sample blog data for now - will be replaced with actual markdown processing later
const samplePosts = [
  {
    id: "future-of-ai-research",
    slug: "future-of-ai-research",
    title: "The Future of AI Research: Trends and Opportunities in 2024",
    excerpt: "Exploring the latest developments in artificial intelligence research and what they mean for the future of technology and society.",
    content: "Full content here...",
    publishedAt: "2024-03-15",
    updatedAt: "2024-03-15",
    tags: ["AI Research", "Machine Learning", "Technology Trends", "Future Tech"],
    category: "research",
    readingTime: 8,
    featured: true,
    author: "Prem Katuwal",
    image: "https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=400&fit=crop"
  },
  {
    id: "building-scalable-web-applications",
    slug: "building-scalable-web-applications",
    title: "Building Scalable Web Applications: Lessons from 2 Years in Industry",
    excerpt: "Key insights and best practices learned during my professional software engineering experience, from junior developer to building production systems.",
    content: "Full content here...",
    publishedAt: "2024-01-20",
    updatedAt: "2024-01-20",
    tags: ["Web Development", "Software Engineering", "Scalability", "Best Practices", "Career"],
    category: "technology",
    readingTime: 6,
    featured: true,
    author: "Prem Katuwal",
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=400&fit=crop"
  },
  {
    id: "phd-journey-begins",
    slug: "phd-journey-begins",
    title: "Starting My PhD Journey: From Industry Back to Academia",
    excerpt: "Reflections on transitioning from professional software engineering to PhD studies, and what motivated this career pivot.",
    content: "Full content here...",
    publishedAt: "2023-09-10",
    updatedAt: "2023-09-10",
    tags: ["PhD", "Career Transition", "Academia", "Personal Growth"],
    category: "career",
    readingTime: 5,
    featured: false,
    author: "Prem Katuwal",
    image: "https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800&h=400&fit=crop"
  }
];

export default function BlogPage() {
  // Process sample data
  const allPosts = samplePosts;
  const categories = Array.from(new Set(samplePosts.map(post => post.category)));
  const tags = Array.from(new Set(samplePosts.flatMap(post => post.tags)));
  const featuredPosts = samplePosts.filter(post => post.featured);
  const stats = {
    totalPosts: samplePosts.length,
    totalCategories: categories.length,
    totalTags: tags.length,
    featuredPosts: featuredPosts.length,
    averageReadingTime: Math.round(samplePosts.reduce((sum, post) => sum + post.readingTime, 0) / samplePosts.length),
  };

  return (
    <BlogPageClient
      allPosts={allPosts}
      categories={categories}
      tags={tags}
      featuredPosts={featuredPosts}
      stats={stats}
    />
  );
}


