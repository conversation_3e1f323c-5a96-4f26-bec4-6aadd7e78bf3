# 📄 How to Convert HTML Resume to PDF

I've created a professional HTML resume for you at `public/resume/Prem_Katuwal_Resume.html`. Here's how to convert it to PDF:

## 🖥️ Method 1: Using Your Browser (Easiest)

1. **Open the HTML file:**
   - Navigate to: `http://localhost:3002/resume/Prem_Katuwal_Resume.html`
   - Or open the file directly in your browser

2. **Print to PDF:**
   - Press `Ctrl+P` (Windows) or `Cmd+P` (Mac)
   - Select "Save as PDF" as the destination
   - Choose these settings:
     - **Layout**: Portrait
     - **Paper size**: A4 or Letter
     - **Margins**: Minimum or None
     - **Scale**: 100%
     - **Background graphics**: Enabled
   - Click "Save" and name it `Prem_Katuwal_Resume.pdf`

3. **Save the PDF:**
   - Save it in the `public/resume/` directory
   - Replace any existing PDF file

## 🛠️ Method 2: Using Online Tools

### Recommended Online Converters:
1. **HTML to PDF Converter** (html-pdf-converter.com)
2. **PDF24** (tools.pdf24.org/en/html-to-pdf)
3. **SmallPDF** (smallpdf.com/html-to-pdf)

**Steps:**
1. Upload the HTML file
2. Convert to PDF
3. Download and save as `Prem_Katuwal_Resume.pdf`

## 💻 Method 3: Using Command Line Tools

### Using wkhtmltopdf:
```bash
# Install wkhtmltopdf first
# Windows: Download from wkhtmltopdf.org
# Mac: brew install wkhtmltopdf
# Linux: sudo apt-get install wkhtmltopdf

# Convert HTML to PDF
wkhtmltopdf --page-size A4 --margin-top 0.5in --margin-bottom 0.5in --margin-left 0.5in --margin-right 0.5in public/resume/Prem_Katuwal_Resume.html public/resume/Prem_Katuwal_Resume.pdf
```

### Using Puppeteer (Node.js):
```bash
# Install puppeteer
npm install puppeteer

# Create conversion script
node -e "
const puppeteer = require('puppeteer');
(async () => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  await page.goto('file://' + __dirname + '/public/resume/Prem_Katuwal_Resume.html');
  await page.pdf({
    path: 'public/resume/Prem_Katuwal_Resume.pdf',
    format: 'A4',
    margin: { top: '0.5in', bottom: '0.5in', left: '0.5in', right: '0.5in' }
  });
  await browser.close();
})();
"
```

## ✅ After Converting to PDF

1. **Verify the PDF:**
   - Check that all content is visible
   - Ensure formatting looks professional
   - Verify contact information is correct

2. **Test the Download:**
   - Visit your portfolio: `http://localhost:3002/about`
   - Click "Download Resume" button
   - Verify the PDF downloads correctly

3. **Update if Needed:**
   - If you need to make changes, edit the HTML file
   - Re-convert to PDF
   - Replace the old PDF file

## 📋 Resume Content Included

Your HTML resume includes:

✅ **Professional Summary** - Highlighting UESTC and AI focus  
✅ **Education** - Master's at UESTC (3rd globally in AI)  
✅ **Professional Experience** - 2+ years software engineering  
✅ **UN Experience** - International collaboration  
✅ **Technical Skills** - Comprehensive skill set  
✅ **Community Impact** - 300+ people trained  
✅ **Key Achievements** - Quantified accomplishments  
✅ **Languages** - English, Chinese, Native language  

## 🎯 Resume Features

- **ATS-Friendly**: Clean format that works with applicant tracking systems
- **Professional Design**: Modern, clean layout with proper typography
- **Quantified Achievements**: Specific metrics and accomplishments
- **UESTC Prominence**: Highlights your prestigious university ranking
- **Contact Information**: Professional contact details
- **Print-Optimized**: Designed to look great on paper and screen

## 📞 Need Help?

If you have trouble converting the HTML to PDF:

1. **Try the browser method first** - it's the most reliable
2. **Check the HTML file** - make sure it displays correctly in your browser
3. **Adjust print settings** - ensure margins and scaling are correct
4. **Contact for support** - if you need assistance with the conversion

---

**🎉 Once converted, your professional resume will be ready to download from your portfolio!**
