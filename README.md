# 🌟 Prem Kat<PERSON>wal's Portfolio

> **Master's Student in Computer Science (AI) at UESTC** - Ranked 3rd globally in AI according to US News & World Report

A world-class, production-ready portfolio website showcasing AI research, software engineering expertise, and community impact. Built with cutting-edge technology and optimized for performance, accessibility, and SEO.

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/Katwal-77/pf)
[![Lighthouse Score](https://img.shields.io/badge/Lighthouse-95%2B-brightgreen)](https://developers.google.com/web/tools/lighthouse)
[![Accessibility](https://img.shields.io/badge/Accessibility-WCAG%202.1%20AA-blue)](https://www.w3.org/WAI/WCAG21/quickref/)

## 🎯 About This Portfolio

This portfolio represents the journey of **Prem Katuwal**, from village beginnings to studying at one of the world's top AI universities. It showcases:

- **🎓 Academic Excellence**: Master's in AI at UESTC (3rd globally ranked in AI)
- **💻 Professional Experience**: 2+ years of software engineering
- **🤝 Community Impact**: Extensive volunteering and mentoring
- **🔬 Research Contributions**: AI research and publications
- **🌍 Global Perspective**: International education and cross-cultural experience

## 🚀 Quick Start

### Prerequisites

- **Node.js**: 18+ (LTS recommended)
- **npm**: 8+ or **yarn**: 1.22+
- **Git**: Latest version

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/Katwal-77/pf.git
cd pf
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
```bash
cp .env.example .env.local
```

4. **Start the development server:**
```bash
npm run dev
```

5. **Open your browser:**
Visit [http://localhost:3002](http://localhost:3002) to see the portfolio.

## 📜 Available Scripts

### Development
```bash
npm run dev          # Start development server (port 3002)
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues automatically
npm run format       # Format code with Prettier
npm run format:check # Check code formatting
npm run type-check   # Run TypeScript type checking
```

### Testing
```bash
npm test                    # Run all tests
npm run test:watch          # Run tests in watch mode
npm run test:coverage       # Generate coverage report
npm run test:ci             # Run tests for CI/CD
npm run test:accessibility  # Run accessibility tests
npm run test:performance    # Run performance tests
npm run test:integration    # Run integration tests
```

### Deployment
```bash
npm run deploy:check    # Pre-deployment validation
npm run deploy          # Deploy to production (Vercel)
npm run deploy:preview  # Deploy preview version
npm run analyze         # Analyze bundle size
npm run lighthouse      # Run Lighthouse audit
npm run sitemap         # Generate sitemap
```

### Monitoring
```bash
# Health check
curl http://localhost:3002/api/health

# Analytics data
curl http://localhost:3002/api/analytics

# Deployment info
curl http://localhost:3002/api/deployment
```

## ✨ Features

### 🎨 **Design & User Experience**
- Modern, responsive design with dark/light mode
- Smooth animations and micro-interactions (Framer Motion)
- Accessibility-first approach (WCAG 2.1 AA compliant)
- Mobile-optimized with touch-friendly interface
- Progressive Web App (PWA) support

### ⚡ **Performance & SEO**
- 95+ Lighthouse scores across all categories
- Core Web Vitals optimized (LCP < 2.5s, FID < 100ms, CLS < 0.1)
- Advanced SEO with structured data and meta optimization
- Image optimization with WebP/AVIF formats
- Font optimization and preloading

### 📊 **Analytics & Monitoring**
- Google Analytics 4 integration
- Custom event tracking for portfolio interactions
- Real-time performance monitoring
- Web Vitals tracking and reporting
- Comprehensive error tracking

### 🧪 **Quality Assurance**
- Comprehensive testing suite (unit, integration, accessibility)
- Automated CI/CD pipeline with GitHub Actions
- Performance budgets and monitoring
- Security headers and best practices
- Code quality enforcement with ESLint and Prettier

## 🏗️ Project Structure

```
pf/
├── src/
│   ├── app/                    # Next.js 14 App Router
│   │   ├── (pages)/           # Page routes
│   │   ├── api/               # API routes
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── ui/               # Base UI components
│   │   ├── features/         # Feature-specific components
│   │   └── layout/           # Layout components
│   ├── data/                 # Static data files
│   ├── lib/                  # Utility functions
│   └── __tests__/            # Test files
├── public/                   # Static assets
├── scripts/                  # Build and deployment scripts
├── .github/workflows/        # GitHub Actions CI/CD
├── vercel.json              # Vercel deployment config
├── lighthouse.config.js     # Performance monitoring
└── DEPLOYMENT.md            # Detailed deployment guide
```

## 🛠️ Technology Stack

### **Core Framework**
- **Next.js 14**: React framework with App Router
- **TypeScript**: Type-safe JavaScript
- **React 19**: Latest React with concurrent features

### **Styling & UI**
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animation library
- **Lucide React**: Beautiful icons
- **Custom Components**: Accessible UI components

### **Development Tools**
- **ESLint**: Code linting and quality
- **Prettier**: Code formatting
- **Husky**: Git hooks for quality gates
- **TypeScript**: Static type checking

### **Testing**
- **Jest**: JavaScript testing framework
- **React Testing Library**: Component testing
- **jest-axe**: Accessibility testing
- **Lighthouse CI**: Performance testing

### **Deployment & Monitoring**
- **Vercel**: Hosting and deployment platform
- **Google Analytics 4**: User analytics
- **Web Vitals**: Performance monitoring
- **GitHub Actions**: CI/CD pipeline

## 🔧 Environment Configuration

### Required Environment Variables

```env
# Site Configuration
NEXT_PUBLIC_SITE_URL=https://premkatuwal.com
NEXT_PUBLIC_SITE_NAME="Prem Katuwal - AI Researcher & Software Engineer"
CONTACT_EMAIL=<EMAIL>
```

### Optional Environment Variables

```env
# Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx

# Email Configuration (for contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Feature Flags
NEXT_PUBLIC_ENABLE_BLOG=true
NEXT_PUBLIC_ENABLE_TESTIMONIALS=true
NEXT_PUBLIC_ENABLE_JOURNEY=true
NEXT_PUBLIC_ENABLE_CONTACT_FORM=true

# Development
NODE_ENV=development
NEXT_PUBLIC_VERCEL_ENV=development
```

## 🚀 Deployment Guide

### Option 1: Vercel (Recommended)

#### Quick Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/Katwal-77/pf)

#### Manual Setup
1. **Install Vercel CLI:**
```bash
npm i -g vercel
```

2. **Login to Vercel:**
```bash
vercel login
```

3. **Deploy:**
```bash
npm run deploy
# or
vercel --prod
```

4. **Configure Environment Variables:**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all variables from `.env.example`

### Option 2: Other Platforms

#### Netlify
1. Connect your GitHub repository
2. Set build command: `npm run build`
3. Set publish directory: `.next`
4. Add environment variables

#### Self-hosted
```bash
# Build the application
npm run build

# Start production server
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start npm --name "portfolio" -- start
```

### Pre-deployment Checklist

Run the deployment validation script:
```bash
npm run deploy:check
```

This will verify:
- ✅ Dependencies are installed
- ✅ TypeScript compilation
- ✅ ESLint passes
- ✅ Tests pass
- ✅ Build succeeds
- ✅ Security audit passes

## 📊 Monitoring & Analytics

### Health Monitoring
- **Health Check**: `/api/health` - Application health status
- **Analytics**: `/api/analytics` - Usage analytics and metrics
- **Deployment**: `/api/deployment` - Build and deployment information

### Performance Monitoring
```bash
# Run Lighthouse audit
npm run lighthouse

# Check Core Web Vitals
curl https://premkatuwal.com/api/analytics?metric=web_vitals

# Monitor real-time performance
curl https://premkatuwal.com/api/health
```

## 🔧 Troubleshooting

### Common Issues

#### Build Failures
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

#### Port Already in Use
```bash
# Kill process on port 3002
npx kill-port 3002

# Or use a different port
npm run dev -- --port 3003
```

#### TypeScript Errors
```bash
# Run type checking
npm run type-check

# Fix common issues
npm run lint:fix
```

#### Performance Issues
```bash
# Analyze bundle size
npm run analyze

# Run performance tests
npm run test:performance

# Check Lighthouse scores
npm run lighthouse
```

### Getting Help

1. Check the [DEPLOYMENT.md](./DEPLOYMENT.md) for detailed deployment instructions
2. Review the [troubleshooting section](#troubleshooting)
3. Check [GitHub Issues](https://github.com/Katwal-77/pf/issues)
4. Contact: <EMAIL>

## 🤝 Contributing

This is a personal portfolio, but suggestions and improvements are welcome!

### Development Workflow

1. **Fork the repository**
2. **Create a feature branch:**
```bash
git checkout -b feature/amazing-feature
```

3. **Make your changes and test:**
```bash
npm run test
npm run lint
npm run type-check
```

4. **Commit your changes:**
```bash
git commit -m "Add amazing feature"
```

5. **Push to your branch:**
```bash
git push origin feature/amazing-feature
```

6. **Open a Pull Request**

### Code Standards

- **TypeScript**: All code must be type-safe
- **ESLint**: Follow the configured linting rules
- **Prettier**: Code must be properly formatted
- **Testing**: New features should include tests
- **Accessibility**: Maintain WCAG 2.1 AA compliance

## 📄 Documentation

- **[DEPLOYMENT.md](./DEPLOYMENT.md)**: Comprehensive deployment guide
- **[.env.example](./.env.example)**: Environment variables template
- **[lighthouse.config.js](./lighthouse.config.js)**: Performance monitoring configuration
- **[vercel.json](./vercel.json)**: Vercel deployment configuration

## 📈 Performance Metrics

### Target Scores
- **Lighthouse Performance**: 95+
- **Lighthouse Accessibility**: 95+
- **Lighthouse Best Practices**: 95+
- **Lighthouse SEO**: 95+

### Core Web Vitals
- **LCP (Largest Contentful Paint)**: < 2.5s
- **INP (Interaction to Next Paint)**: < 200ms
- **CLS (Cumulative Layout Shift)**: < 0.1

## 🌟 Acknowledgments

- **UESTC**: University of Electronic Science and Technology of China
- **Next.js Team**: For the amazing framework
- **Vercel**: For hosting and deployment platform
- **Open Source Community**: For the incredible tools and libraries

## 📞 Contact

**Prem Katuwal**
- **Email**: <EMAIL>
- **GitHub**: [@Katwal-77](https://github.com/Katwal-77)
- **LinkedIn**: [premkatuwal](https://linkedin.com/in/premkatuwal)
- **Portfolio**: [premkatuwal.com](https://premkatuwal.com)

---

**🎉 Thank you for visiting my portfolio!**

This project represents my journey from village beginnings to studying at one of the world's top AI universities. It showcases not just technical skills, but also the power of perseverance, education, and community impact.

*Built with ❤️ by Prem Katuwal*