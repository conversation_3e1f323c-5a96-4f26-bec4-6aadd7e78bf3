@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --card: #ffffff;
  --card-foreground: #0f172a;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
  --radius: 0.5rem;
}

/* Dark theme colors when .dark class is applied */
.dark {
  --background: #0f172a;
  --foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --border: #334155;
  --input: #334155;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --accent: #10b981;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #3b82f6;
}

/* System preference fallback */
@media (prefers-color-scheme: dark) {
  :root:not(.light) {
    --background: #0f172a;
    --foreground: #f8fafc;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --card: #1e293b;
    --card-foreground: #f8fafc;
    --border: #334155;
    --input: #334155;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #334155;
    --secondary-foreground: #f8fafc;
    --accent: #10b981;
    --accent-foreground: #ffffff;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --ring: #3b82f6;
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-ring: var(--ring);
  --font-sans: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  --radius: var(--radius);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 16px;
  line-height: 1.7;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Improved typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  color: var(--foreground);
  margin-bottom: 0.5em;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2rem;
  font-weight: 600;
}

h3 {
  font-size: 1.5rem;
}

h4 {
  font-size: 1.25rem;
}

p {
  font-size: 1rem;
  line-height: 1.7;
  color: var(--foreground);
  margin-bottom: 1rem;
}

/* Improved text contrast */
.text-muted {
  color: var(--muted-foreground);
}

.text-primary {
  color: var(--primary);
}

.text-accent {
  color: var(--accent);
}

/* Better link styles */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent);
}

/* Improved focus styles */
a:focus-visible,
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Better text selection */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* Improved readability for small text */
.text-sm {
  font-size: 0.875rem;
  line-height: 1.6;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1.5;
}

/* Better spacing for content */
.prose {
  max-width: 65ch;
  line-height: 1.7;
}

.prose p {
  margin-bottom: 1.25rem;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.3;
}

.prose h1 {
  margin-top: 0;
}

/* Dark mode specific improvements */
.dark {
  color-scheme: dark;
}

.dark .text-muted-foreground {
  color: #94a3b8;
}

.dark h1,
.dark h2,
.dark h3,
.dark h4,
.dark h5,
.dark h6 {
  color: #f8fafc;
}

.dark p {
  color: #e2e8f0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}

/* Focus styles */
*:focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
