{"name": "jest-matcher-utils", "description": "A set of utility functions for expect and related packages", "version": "29.2.2", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-matcher-utils"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.2.1", "jest-get-type": "^29.2.0", "pretty-format": "^29.2.1"}, "devDependencies": {"@jest/test-utils": "^29.2.1", "@types/node": "*"}, "publishConfig": {"access": "public"}, "gitHead": "0a8edbe0ac434394a16cc173a03ff54a9cc50e41"}