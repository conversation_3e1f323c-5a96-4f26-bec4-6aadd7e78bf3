{"name": "jest-axe", "version": "10.0.0", "description": "Custom Jest matcher for aXe for testing accessibility", "repository": "nickcolley/jest-axe", "main": "index.js", "files": ["index.js", "extend-expect.js"], "scripts": {"test": "npm run lint && npm run jest", "jest": "FORCE_COLOR=0 jest", "lint": "eslint *.js __tests__/**/*.js && prettier --check *.js __tests__/**/*.js"}, "keywords": ["jest", "matcher", "axe", "accessibility", "a11y"], "author": "<PERSON>", "license": "MIT", "engines": {"node": ">= 16.0.0"}, "dependencies": {"axe-core": "4.10.2", "chalk": "4.1.2", "jest-matcher-utils": "29.2.2", "lodash.merge": "4.6.2"}, "devDependencies": {"@testing-library/react": "^13.4.0", "@testing-library/vue": "^6.6.1", "@vue/test-utils": "^2.2.1", "eslint": "^8.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jest": "^27.1.3", "eslint-plugin-jest-dom": "^4.0.2", "eslint-plugin-n": "^15.4.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.2.2", "jest-environment-jsdom": "^29.2.2", "prettier": "^2.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "vue": "^3.2.41"}, "jest": {"testEnvironment": "jsdom", "testEnvironmentOptions": {"customExportConditions": ["node", "node-addons"]}}, "eslintConfig": {"globals": {"document": true}, "env": {"shared-node-browser": true, "jest/globals": true}, "extends": ["eslint:recommended", "plugin:n/recommended", "prettier", "plugin:jest/recommended", "plugin:jest-dom/recommended"], "plugins": ["prettier", "jest", "jest-dom"], "rules": {"prettier/prettier": "error"}, "parserOptions": {"ecmaVersion": 2022}}}