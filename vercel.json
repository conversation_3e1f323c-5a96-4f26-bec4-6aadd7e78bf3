{"buildCommand": "npm run build", "outputDirectory": ".next", "framework": "nextjs", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1", "hnd1", "syd1"], "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://premkatuwal.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/home", "destination": "/", "permanent": true}, {"source": "/portfolio", "destination": "/projects", "permanent": true}, {"source": "/cv", "destination": "/about", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "crons": [{"path": "/api/analytics/daily-report", "schedule": "0 9 * * *"}]}