# 🚀 Deployment Guide - Pre<PERSON> Katuwal's Portfolio

This guide covers the complete deployment process for Pre<PERSON> Katuwal's portfolio website, including setup, configuration, monitoring, and maintenance.

## 📋 Prerequisites

- Node.js 18+ installed
- Git repository set up
- Vercel account (recommended) or alternative hosting platform
- Google Analytics account (optional but recommended)
- Custom domain (optional)

## 🔧 Environment Setup

### 1. Environment Variables

Copy `.env.example` to `.env.local` and configure:

```bash
cp .env.example .env.local
```

**Required Variables:**
```env
NEXT_PUBLIC_SITE_URL=https://premkatuwal.com
NEXT_PUBLIC_SITE_NAME="Prem Katuwal - AI Researcher & Software Engineer"
CONTACT_EMAIL=<EMAIL>
```

**Analytics (Recommended):**
```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx
```

**Email Configuration:**
```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### 2. Pre-deployment Checklist

Run the deployment script to verify everything is ready:

```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh
```

This script will:
- ✅ Check Node.js version
- ✅ Install dependencies
- ✅ Run TypeScript checks
- ✅ Execute linting
- ✅ Run test suite
- ✅ Build the application
- ✅ Verify security configuration
- ✅ Check for sensitive data

## 🌐 Deployment Options

### Option 1: Vercel (Recommended)

#### Quick Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/Katwal-77/pf)

#### Manual Setup

1. **Install Vercel CLI:**
```bash
npm i -g vercel
```

2. **Login to Vercel:**
```bash
vercel login
```

3. **Deploy:**
```bash
vercel --prod
```

4. **Configure Environment Variables:**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all variables from `.env.example`

#### Vercel Configuration

The `vercel.json` file includes:
- **Security Headers**: XSS protection, content type options
- **Redirects**: SEO-friendly URL redirects
- **Caching**: Optimized cache headers
- **Functions**: API route configuration
- **Regions**: Global edge deployment

### Option 2: Netlify

1. **Connect Repository:**
   - Go to Netlify Dashboard
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Build Settings:**
   - Build command: `npm run build`
   - Publish directory: `.next`
   - Node version: 18

3. **Environment Variables:**
   - Add all variables from `.env.example`

### Option 3: Self-hosted

1. **Build the application:**
```bash
npm run build
```

2. **Start production server:**
```bash
npm start
```

3. **Use PM2 for process management:**
```bash
npm install -g pm2
pm2 start npm --name "portfolio" -- start
pm2 save
pm2 startup
```

## 🔍 Monitoring & Analytics

### Health Monitoring

The portfolio includes built-in health monitoring:

- **Health Check**: `/api/health`
- **Analytics**: `/api/analytics`
- **Deployment Info**: `/api/deployment`

### Google Analytics 4

1. **Create GA4 Property:**
   - Go to Google Analytics
   - Create new property for your domain
   - Copy Measurement ID

2. **Configure Environment:**
```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

3. **Verify Tracking:**
   - Visit your site
   - Check Real-time reports in GA4

### Vercel Analytics

1. **Enable in Vercel Dashboard:**
   - Go to Project → Analytics
   - Enable Web Analytics

2. **Add Environment Variable:**
```env
NEXT_PUBLIC_VERCEL_ANALYTICS_ID=prj_xxxxxxxxxx
```

### Uptime Monitoring

Recommended services:
- **UptimeRobot**: Free tier available
- **Pingdom**: Comprehensive monitoring
- **StatusCake**: Global monitoring

Monitor these endpoints:
- `https://premkatuwal.com/` (main site)
- `https://premkatuwal.com/api/health` (health check)

## 🔒 Security Configuration

### Security Headers

Configured in `vercel.json`:
- `X-Content-Type-Options: nosniff`
- `X-Frame-Options: DENY`
- `X-XSS-Protection: 1; mode=block`
- `Referrer-Policy: strict-origin-when-cross-origin`

### HTTPS & SSL

- **Vercel**: Automatic SSL certificates
- **Netlify**: Automatic SSL certificates
- **Self-hosted**: Use Let's Encrypt or Cloudflare

### Content Security Policy

Add to `next.config.js` for enhanced security:
```javascript
const securityHeaders = [
  {
    key: 'Content-Security-Policy',
    value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' *.googletagmanager.com; style-src 'self' 'unsafe-inline';"
  }
]
```

## 🚀 Performance Optimization

### Core Web Vitals

The portfolio is optimized for:
- **LCP**: < 2.5s (Large Contentful Paint)
- **INP**: < 200ms (Interaction to Next Paint)
- **CLS**: < 0.1 (Cumulative Layout Shift)

### Optimization Features

- ✅ Next.js Image Optimization
- ✅ Font optimization with `next/font`
- ✅ Bundle analysis and tree shaking
- ✅ Static generation where possible
- ✅ Efficient caching strategies

### Performance Monitoring

Monitor performance with:
- Vercel Analytics (Web Vitals)
- Google PageSpeed Insights
- Lighthouse CI
- Real User Monitoring (RUM)

## 🔄 CI/CD Pipeline

### GitHub Actions (Recommended)

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy Portfolio
on:
  push:
    branches: [main]
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### Automated Testing

The pipeline includes:
- Unit tests
- Integration tests
- Accessibility tests
- Performance tests
- Security scans

## 📊 Analytics Dashboard

Access analytics data via API:

```bash
# Get overview
curl https://premkatuwal.com/api/analytics

# Get specific metrics
curl https://premkatuwal.com/api/analytics?metric=traffic_sources&timeframe=30d

# Health check
curl https://premkatuwal.com/api/health
```

## 🛠 Maintenance

### Regular Tasks

1. **Weekly:**
   - Check analytics reports
   - Review error logs
   - Monitor performance metrics

2. **Monthly:**
   - Update dependencies
   - Review security headers
   - Backup configuration

3. **Quarterly:**
   - Performance audit
   - SEO review
   - Content updates

### Troubleshooting

**Build Failures:**
```bash
# Clear cache and rebuild
rm -rf .next node_modules
npm install
npm run build
```

**Performance Issues:**
```bash
# Analyze bundle
npm run build
npx @next/bundle-analyzer
```

**SSL Issues:**
- Check domain configuration
- Verify DNS settings
- Contact hosting provider

## 📞 Support

For deployment issues:

1. Check the [troubleshooting guide](#troubleshooting)
2. Review Vercel/Netlify documentation
3. Check GitHub Issues
4. Contact hosting provider support

## 🎯 Success Metrics

Track these KPIs post-deployment:

- **Performance**: Core Web Vitals scores
- **SEO**: Search rankings and organic traffic
- **User Experience**: Bounce rate and session duration
- **Conversions**: Contact form submissions and resume downloads
- **Technical**: Uptime and error rates

---

**🎉 Congratulations!** Your portfolio is now live and optimized for performance, SEO, and user experience.

For updates and improvements, refer to the main README.md file.
