'use client';

import Link from 'next/link';
import { Github, Linkedin, Twitter, Mail, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Typography } from '@/components/ui/Typography';
import { NAVIGATION_ITEMS, SOCIAL_LINKS } from '@/lib/constants';

export function Footer() {
  const currentYear = new Date().getFullYear();

  const socialIcons = {
    github: Github,
    linkedin: Linkedin,
    twitter: Twitter,
    email: Mail,
  };

  return (
    <footer className="border-t bg-muted/50">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <span className="font-bold text-xl bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                Prem <PERSON>
              </span>
            </Link>
            <Typography variant="muted" className="max-w-xs">
              Computer Science PhD student passionate about AI research, 
              software development, and knowledge sharing.
            </Typography>
          </div>

          {/* Navigation */}
          <div className="space-y-4">
            <Typography variant="h6">Navigation</Typography>
            <nav className="flex flex-col space-y-2">
              {NAVIGATION_ITEMS.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="text-sm text-muted-foreground hover:text-primary transition-colors"
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <Typography variant="h6">Quick Links</Typography>
            <nav className="flex flex-col space-y-2">
              <Link
                href="/resume.pdf"
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-muted-foreground hover:text-primary transition-colors inline-flex items-center gap-1"
              >
                Resume <ExternalLink className="h-3 w-3" />
              </Link>
              <Link
                href="/blog"
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                Latest Posts
              </Link>
              <Link
                href="/research"
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                Publications
              </Link>
              <Link
                href="/projects"
                className="text-sm text-muted-foreground hover:text-primary transition-colors"
              >
                Featured Projects
              </Link>
            </nav>
          </div>

          {/* Contact & Social */}
          <div className="space-y-4">
            <Typography variant="h6">Connect</Typography>
            <div className="flex flex-col space-y-3">
              <div className="flex space-x-2">
                <Button
                  href={`${SOCIAL_LINKS.github}/Katwal-77`}
                  variant="ghost"
                  size="icon"
                  external
                  aria-label="GitHub"
                >
                  <Github className="h-4 w-4" />
                </Button>
                <Button
                  href={`${SOCIAL_LINKS.linkedin}/premkatuwal`}
                  variant="ghost"
                  size="icon"
                  external
                  aria-label="LinkedIn"
                >
                  <Linkedin className="h-4 w-4" />
                </Button>
                <Button
                  href={`${SOCIAL_LINKS.twitter}/premkatuwal`}
                  variant="ghost"
                  size="icon"
                  external
                  aria-label="Twitter"
                >
                  <Twitter className="h-4 w-4" />
                </Button>
                <Button
                  href="mailto:<EMAIL>"
                  variant="ghost"
                  size="icon"
                  aria-label="Email"
                >
                  <Mail className="h-4 w-4" />
                </Button>
              </div>
              <Button href="/contact" size="sm" className="w-fit">
                Get in Touch
              </Button>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-8 pt-8 border-t flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
          <Typography variant="muted">
            © {currentYear} Prem Katuwal. Built with Next.js and Tailwind CSS.
          </Typography>
          <div className="flex items-center space-x-4">
            <Link
              href="/privacy"
              className="text-xs text-muted-foreground hover:text-primary transition-colors"
            >
              Privacy Policy
            </Link>
            <Link
              href="/terms"
              className="text-xs text-muted-foreground hover:text-primary transition-colors"
            >
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
