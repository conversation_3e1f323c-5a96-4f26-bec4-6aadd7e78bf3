{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport { forwardRef } from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst cardVariants = cva(\n  'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-200',\n  {\n    variants: {\n      variant: {\n        default: 'border-border',\n        outlined: 'border-2 border-border',\n        elevated: 'shadow-lg hover:shadow-xl',\n      },\n      padding: {\n        none: '',\n        sm: 'p-4',\n        md: 'p-6',\n        lg: 'p-8',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      padding: 'md',\n    },\n  }\n);\n\nexport interface CardProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof cardVariants> {}\n\nconst Card = forwardRef<HTMLDivElement, CardProps>(\n  ({ className, variant, padding, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(cardVariants({ variant, padding, className }))}\n      {...props}\n    />\n  )\n);\n\nCard.displayName = 'Card';\n\nconst CardHeader = forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\n\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\n\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\n\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\n\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\n\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,eAAe,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACrB,wFACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,UAAU;YACV,UAAU;QACZ;QACA,SAAS;YACP,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,SAAS;IACX;AACF;AAOF,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACpB,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;YAAS;YAAS;QAAU;QACxD,GAAG,KAAK;;;;;;AAKf,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAGzB,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAIb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAIb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAGhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAIb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/AboutHero.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Download, MapPin, Calendar, GraduationCap } from 'lucide-react';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Typography } from '@/components/ui/Typography';\nimport { ANIMATION_VARIANTS } from '@/lib/constants';\n\ninterface AboutHeroProps {\n  name: string;\n  title: string;\n  bio: string;\n  location: string;\n  profileImage?: string;\n  resumeUrl?: string;\n  currentStatus?: string;\n  education?: string;\n}\n\nexport function AboutHero({\n  name,\n  title,\n  bio,\n  location,\n  profileImage,\n  resumeUrl,\n  currentStatus = \"PhD Student\",\n  education = \"Computer Science\"\n}: AboutHeroProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n        delayChildren: 0.1,\n      },\n    },\n  };\n\n  const quickFacts = [\n    {\n      icon: GraduationCap,\n      label: \"Current Status\",\n      value: currentStatus,\n    },\n    {\n      icon: Calendar,\n      label: \"Field of Study\",\n      value: education,\n    },\n    {\n      icon: MapPin,\n      label: \"Location\",\n      value: location,\n    },\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-background via-muted/30 to-background\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\"\n        >\n          {/* Profile Image */}\n          <motion.div\n            variants={ANIMATION_VARIANTS.slideRight}\n            className=\"flex justify-center lg:justify-start order-2 lg:order-1\"\n          >\n            <div className=\"relative\">\n              <motion.div\n                className=\"w-80 h-80 lg:w-96 lg:h-96 rounded-2xl overflow-hidden shadow-2xl\"\n                whileHover={{ scale: 1.02 }}\n                transition={{ duration: 0.3 }}\n              >\n                {profileImage ? (\n                  <img\n                    src={profileImage}\n                    alt={`${name} - Profile`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                ) : (\n                  <div className=\"w-full h-full bg-gradient-to-br from-primary/20 to-accent/20 flex items-center justify-center\">\n                    <Typography variant=\"h1\" className=\"text-6xl text-muted-foreground\">\n                      {name.split(' ').map(n => n[0]).join('')}\n                    </Typography>\n                  </div>\n                )}\n              </motion.div>\n\n              {/* Floating Badge */}\n              <motion.div\n                className=\"absolute -bottom-4 -right-4 bg-primary text-primary-foreground px-4 py-2 rounded-full shadow-lg\"\n                animate={{\n                  y: [-5, 5, -5],\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                }}\n              >\n                <Typography variant=\"small\" className=\"font-medium\">\n                  Available for opportunities\n                </Typography>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Content */}\n          <motion.div\n            variants={ANIMATION_VARIANTS.slideLeft}\n            className=\"space-y-8 order-1 lg:order-2\"\n          >\n            {/* Header */}\n            <div className=\"space-y-4\">\n              <Typography variant=\"large\" className=\"text-primary font-medium\">\n                About Me\n              </Typography>\n              <Typography variant=\"h1\" className=\"text-4xl lg:text-5xl font-bold\">\n                {name}\n              </Typography>\n              <Typography variant=\"h3\" className=\"text-xl lg:text-2xl text-muted-foreground font-normal\">\n                {title}\n              </Typography>\n            </div>\n\n            {/* Bio */}\n            <Typography variant=\"lead\" className=\"text-lg leading-relaxed\">\n              {bio}\n            </Typography>\n\n            {/* Quick Facts */}\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n              {quickFacts.map((fact, index) => (\n                <motion.div\n                  key={fact.label}\n                  variants={ANIMATION_VARIANTS.slideUp}\n                  whileHover={{ scale: 1.05 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <Card className=\"text-center p-4 hover:shadow-md transition-all duration-300\">\n                    <CardContent className=\"space-y-2 p-0\">\n                      <fact.icon className=\"h-6 w-6 mx-auto text-primary\" />\n                      <Typography variant=\"small\" className=\"text-muted-foreground\">\n                        {fact.label}\n                      </Typography>\n                      <Typography variant=\"small\" className=\"font-medium\">\n                        {fact.value}\n                      </Typography>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              {resumeUrl && (\n                <Button href={resumeUrl} size=\"lg\" external className=\"group\">\n                  <Download className=\"mr-2 h-4 w-4 transition-transform group-hover:translate-y-0.5\" />\n                  Download Resume\n                </Button>\n              )}\n              <Button href=\"/contact\" variant=\"outline\" size=\"lg\">\n                Get in Touch\n              </Button>\n            </div>\n\n            {/* Additional Info */}\n            <div className=\"p-6 bg-muted/50 rounded-lg border\">\n              <Typography variant=\"p\" className=\"text-muted-foreground\">\n                <strong>Fun fact:</strong> When I'm not diving deep into research or coding, \n                you'll find me volunteering in my community, exploring new technologies, \n                or sharing knowledge through writing and mentoring.\n              </Typography>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAoBO,SAAS,UAAU,EACxB,IAAI,EACJ,KAAK,EACL,GAAG,EACH,QAAQ,EACR,YAAY,EACZ,SAAS,EACT,gBAAgB,aAAa,EAC7B,YAAY,kBAAkB,EACf;IACf,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,aAAa;QACjB;YACE,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,OAAO;QACT;QACA;YACE,MAAM,0MAAA,CAAA,SAAM;YACZ,OAAO;YACP,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,UAAU;gBACV,SAAQ;gBACR,SAAQ;gBACR,WAAU;;kCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU,uHAAA,CAAA,qBAAkB,CAAC,UAAU;wBACvC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,YAAY;wCAAE,UAAU;oCAAI;8CAE3B,6BACC,8OAAC;wCACC,KAAK;wCACL,KAAK,GAAG,KAAK,UAAU,CAAC;wCACxB,WAAU;;;;;6DAGZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;4CAAC,SAAQ;4CAAK,WAAU;sDAChC,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;;;;;;8CAO7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCACP,GAAG;4CAAC,CAAC;4CAAG;4CAAG,CAAC;yCAAE;oCAChB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;8CAEA,cAAA,8OAAC,sIAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;kCAQ1D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU,uHAAA,CAAA,qBAAkB,CAAC,SAAS;wBACtC,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sIAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAQ,WAAU;kDAA2B;;;;;;kDAGjE,8OAAC,sIAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,WAAU;kDAChC;;;;;;kDAEH,8OAAC,sIAAA,CAAA,aAAU;wCAAC,SAAQ;wCAAK,WAAU;kDAChC;;;;;;;;;;;;0CAKL,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAO,WAAU;0CAClC;;;;;;0CAIH,8OAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wCACpC,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC,sIAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,WAAU;kEACnC,KAAK,KAAK;;;;;;kEAEb,8OAAC,sIAAA,CAAA,aAAU;wDAAC,SAAQ;wDAAQ,WAAU;kEACnC,KAAK,KAAK;;;;;;;;;;;;;;;;;uCAZZ,KAAK,KAAK;;;;;;;;;;0CAqBrB,8OAAC;gCAAI,WAAU;;oCACZ,2BACC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAM;wCAAW,MAAK;wCAAK,QAAQ;wCAAC,WAAU;;0DACpD,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAkE;;;;;;;kDAI1F,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAW,SAAQ;wCAAU,MAAK;kDAAK;;;;;;;;;;;;0CAMtD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oCAAC,SAAQ;oCAAI,WAAU;;sDAChC,8OAAC;sDAAO;;;;;;wCAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1C", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Timeline.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Calendar, MapPin, Award, ExternalLink } from 'lucide-react';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Typography } from '@/components/ui/Typography';\nimport { Button } from '@/components/ui/Button';\nimport { ANIMATION_VARIANTS } from '@/lib/constants';\nimport type { TimelineEvent } from '@/types';\n\ninterface TimelineProps {\n  events: TimelineEvent[];\n  title?: string;\n  description?: string;\n}\n\nexport function Timeline({ events, title = \"My Journey\", description }: TimelineProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const getTypeIcon = (type: string) => {\n    switch (type) {\n      case 'education':\n        return '🎓';\n      case 'work':\n        return '💼';\n      case 'research':\n        return '🔬';\n      case 'volunteering':\n        return '🤝';\n      default:\n        return '📋';\n    }\n  };\n\n  const getTypeColor = (type: string) => {\n    switch (type) {\n      case 'education':\n        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';\n      case 'work':\n        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';\n      case 'research':\n        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';\n      case 'volunteering':\n        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';\n      default:\n        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n    });\n  };\n\n  const sortedEvents = [...events].sort((a, b) => \n    new Date(b.startDate).getTime() - new Date(a.startDate).getTime()\n  );\n\n  return (\n    <section className=\"py-20\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          variants={containerVariants}\n          className=\"space-y-12\"\n        >\n          {/* Section Header */}\n          <motion.div variants={ANIMATION_VARIANTS.slideUp} className=\"text-center space-y-4\">\n            <Typography variant=\"h2\" className=\"text-3xl lg:text-4xl font-bold\">\n              {title}\n            </Typography>\n            {description && (\n              <Typography variant=\"lead\" className=\"max-w-2xl mx-auto\">\n                {description}\n              </Typography>\n            )}\n          </motion.div>\n\n          {/* Timeline */}\n          <div className=\"relative\">\n            {/* Timeline Line */}\n            <div className=\"absolute left-4 md:left-1/2 top-0 bottom-0 w-px bg-border transform md:-translate-x-px\" />\n\n            <motion.div variants={containerVariants} className=\"space-y-8\">\n              {sortedEvents.map((event, index) => (\n                <motion.div\n                  key={event.id}\n                  variants={ANIMATION_VARIANTS.slideUp}\n                  className={`relative flex items-center ${\n                    index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'\n                  }`}\n                >\n                  {/* Timeline Dot */}\n                  <div className=\"absolute left-4 md:left-1/2 w-3 h-3 bg-primary rounded-full transform -translate-x-1/2 z-10\">\n                    <div className=\"absolute inset-0 bg-primary rounded-full animate-ping opacity-75\" />\n                  </div>\n\n                  {/* Content */}\n                  <div className={`w-full md:w-1/2 ${\n                    index % 2 === 0 ? 'md:pr-8 pl-12 md:pl-0' : 'md:pl-8 pl-12 md:pr-0'\n                  }`}>\n                    <motion.div\n                      whileHover={{ scale: 1.02 }}\n                      transition={{ duration: 0.2 }}\n                    >\n                      <Card className=\"hover:shadow-lg transition-all duration-300\">\n                        <CardHeader className=\"space-y-3\">\n                          <div className=\"flex items-center justify-between flex-wrap gap-2\">\n                            <span\n                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(\n                                event.type\n                              )}`}\n                            >\n                              <span className=\"mr-1\">{getTypeIcon(event.type)}</span>\n                              {event.type.charAt(0).toUpperCase() + event.type.slice(1)}\n                            </span>\n                            {event.current && (\n                              <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground\">\n                                Current\n                              </span>\n                            )}\n                          </div>\n\n                          <CardTitle className=\"text-xl\">{event.title}</CardTitle>\n                          <CardDescription className=\"text-lg font-medium text-primary\">\n                            {event.organization}\n                          </CardDescription>\n\n                          <div className=\"flex items-center space-x-4 text-sm text-muted-foreground\">\n                            <div className=\"flex items-center space-x-1\">\n                              <Calendar className=\"h-4 w-4\" />\n                              <span>\n                                {formatDate(event.startDate)} - {\n                                  event.endDate ? formatDate(event.endDate) : 'Present'\n                                }\n                              </span>\n                            </div>\n                            {event.location && (\n                              <div className=\"flex items-center space-x-1\">\n                                <MapPin className=\"h-4 w-4\" />\n                                <span>{event.location}</span>\n                              </div>\n                            )}\n                          </div>\n                        </CardHeader>\n\n                        <CardContent className=\"space-y-4\">\n                          <Typography variant=\"p\" className=\"text-muted-foreground\">\n                            {event.description}\n                          </Typography>\n\n                          {event.achievements && event.achievements.length > 0 && (\n                            <div className=\"space-y-2\">\n                              <div className=\"flex items-center space-x-2\">\n                                <Award className=\"h-4 w-4 text-accent\" />\n                                <Typography variant=\"small\" className=\"font-medium\">\n                                  Key Achievements\n                                </Typography>\n                              </div>\n                              <ul className=\"space-y-1 ml-6\">\n                                {event.achievements.map((achievement, idx) => (\n                                  <li key={idx} className=\"text-sm text-muted-foreground\">\n                                    • {achievement}\n                                  </li>\n                                ))}\n                              </ul>\n                            </div>\n                          )}\n                        </CardContent>\n                      </Card>\n                    </motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AAEA;AAPA;;;;;;;AAgBO,SAAS,SAAS,EAAE,MAAM,EAAE,QAAQ,YAAY,EAAE,WAAW,EAAiB;IACnF,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;QACT;IACF;IAEA,MAAM,eAAe;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IACxC,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IAGjE,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAS;gBACzC,UAAU;gBACV,WAAU;;kCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wBAAE,WAAU;;0CAC1D,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,WAAU;0CAChC;;;;;;4BAEF,6BACC,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAO,WAAU;0CAClC;;;;;;;;;;;;kCAMP,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;;;;;0CAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,UAAU;gCAAmB,WAAU;0CAChD,aAAa,GAAG,CAAC,CAAC,OAAO,sBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wCACpC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,MAAM,IAAI,gBAAgB,uBAClC;;0DAGF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAIjB,8OAAC;gDAAI,WAAW,CAAC,gBAAgB,EAC/B,QAAQ,MAAM,IAAI,0BAA0B,yBAC5C;0DACA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,YAAY;wDAAE,OAAO;oDAAK;oDAC1B,YAAY;wDAAE,UAAU;oDAAI;8DAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;wDAAC,WAAU;;0EACd,8OAAC,gIAAA,CAAA,aAAU;gEAAC,WAAU;;kFACpB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFACC,WAAW,CAAC,wEAAwE,EAAE,aACpF,MAAM,IAAI,GACT;;kGAEH,8OAAC;wFAAK,WAAU;kGAAQ,YAAY,MAAM,IAAI;;;;;;oFAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;4EAExD,MAAM,OAAO,kBACZ,8OAAC;gFAAK,WAAU;0FAAuG;;;;;;;;;;;;kFAM3H,8OAAC,gIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAW,MAAM,KAAK;;;;;;kFAC3C,8OAAC,gIAAA,CAAA,kBAAe;wEAAC,WAAU;kFACxB,MAAM,YAAY;;;;;;kFAGrB,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,0MAAA,CAAA,WAAQ;wFAAC,WAAU;;;;;;kGACpB,8OAAC;;4FACE,WAAW,MAAM,SAAS;4FAAE;4FAC3B,MAAM,OAAO,GAAG,WAAW,MAAM,OAAO,IAAI;;;;;;;;;;;;;4EAIjD,MAAM,QAAQ,kBACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,0MAAA,CAAA,SAAM;wFAAC,WAAU;;;;;;kGAClB,8OAAC;kGAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0EAM7B,8OAAC,gIAAA,CAAA,cAAW;gEAAC,WAAU;;kFACrB,8OAAC,sIAAA,CAAA,aAAU;wEAAC,SAAQ;wEAAI,WAAU;kFAC/B,MAAM,WAAW;;;;;;oEAGnB,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,MAAM,GAAG,mBACjD,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;kGACjB,8OAAC,sIAAA,CAAA,aAAU;wFAAC,SAAQ;wFAAQ,WAAU;kGAAc;;;;;;;;;;;;0FAItD,8OAAC;gFAAG,WAAU;0FACX,MAAM,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,oBACpC,8OAAC;wFAAa,WAAU;;4FAAgC;4FACnD;;uFADI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCA3EpB,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8F/B", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Stats.tsx"], "sourcesContent": ["'use client';\n\nimport { motion, useInView } from 'framer-motion';\nimport { useRef, useEffect, useState } from 'react';\nimport { Typography } from '@/components/ui/Typography';\nimport { ANIMATION_VARIANTS } from '@/lib/constants';\n\ninterface Stat {\n  id: string;\n  value: number;\n  label: string;\n  suffix?: string;\n  prefix?: string;\n  description?: string;\n}\n\ninterface StatsProps {\n  stats: Stat[];\n}\n\nfunction AnimatedCounter({ \n  value, \n  duration = 2000 \n}: { \n  value: number; \n  duration?: number; \n}) {\n  const [count, setCount] = useState(0);\n  const ref = useRef<HTMLSpanElement>(null);\n  const isInView = useInView(ref, { once: true, margin: \"-100px\" });\n\n  useEffect(() => {\n    if (!isInView) return;\n\n    let startTime: number;\n    let animationFrame: number;\n\n    const animate = (timestamp: number) => {\n      if (!startTime) startTime = timestamp;\n      const progress = Math.min((timestamp - startTime) / duration, 1);\n      \n      // Easing function for smooth animation\n      const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n      setCount(Math.floor(easeOutQuart * value));\n\n      if (progress < 1) {\n        animationFrame = requestAnimationFrame(animate);\n      }\n    };\n\n    animationFrame = requestAnimationFrame(animate);\n\n    return () => {\n      if (animationFrame) {\n        cancelAnimationFrame(animationFrame);\n      }\n    };\n  }, [isInView, value, duration]);\n\n  return <span ref={ref}>{count}</span>;\n}\n\nexport function Stats({ stats }: StatsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  return (\n    <section className=\"py-20 bg-background\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          variants={containerVariants}\n          className=\"space-y-12\"\n        >\n          {/* Section Header */}\n          <motion.div variants={ANIMATION_VARIANTS.slideUp} className=\"text-center space-y-4\">\n            <Typography variant=\"h2\" className=\"text-3xl lg:text-4xl font-bold\">\n              Impact & Achievements\n            </Typography>\n            <Typography variant=\"lead\" className=\"max-w-2xl mx-auto\">\n              Numbers that reflect my commitment to research excellence, \n              community engagement, and professional growth.\n            </Typography>\n          </motion.div>\n\n          {/* Stats Grid */}\n          <motion.div\n            variants={containerVariants}\n            className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.id}\n                variants={ANIMATION_VARIANTS.slideUp}\n                className=\"text-center space-y-2 group\"\n              >\n                <motion.div\n                  className=\"relative\"\n                  whileHover={{ scale: 1.05 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  {/* Background Circle */}\n                  <div className=\"absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300\" />\n                  \n                  {/* Stat Value */}\n                  <div className=\"relative bg-background border border-border rounded-full w-32 h-32 mx-auto flex items-center justify-center group-hover:border-primary/50 transition-colors duration-300\">\n                    <Typography variant=\"h2\" className=\"text-2xl lg:text-3xl font-bold text-primary\">\n                      {stat.prefix}\n                      <AnimatedCounter value={stat.value} />\n                      {stat.suffix}\n                    </Typography>\n                  </div>\n                </motion.div>\n\n                {/* Stat Label */}\n                <div className=\"space-y-1\">\n                  <Typography variant=\"h4\" className=\"text-lg font-semibold\">\n                    {stat.label}\n                  </Typography>\n                  {stat.description && (\n                    <Typography variant=\"muted\" className=\"text-sm\">\n                      {stat.description}\n                    </Typography>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Additional Context */}\n          <motion.div\n            variants={ANIMATION_VARIANTS.slideUp}\n            className=\"text-center pt-8\"\n          >\n            <div className=\"max-w-4xl mx-auto p-6 bg-muted/50 rounded-lg border\">\n              <Typography variant=\"p\" className=\"text-muted-foreground\">\n                These metrics represent my journey in academia and technology, \n                showcasing not just individual achievements but also my commitment \n                to collaborative research, community building, and knowledge sharing. \n                Each number tells a story of dedication, learning, and impact.\n              </Typography>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAoBA,SAAS,gBAAgB,EACvB,KAAK,EACL,WAAW,IAAI,EAIhB;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAmB;IACpC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE,MAAM;QAAM,QAAQ;IAAS;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,IAAI;QACJ,IAAI;QAEJ,MAAM,UAAU,CAAC;YACf,IAAI,CAAC,WAAW,YAAY;YAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,YAAY,SAAS,IAAI,UAAU;YAE9D,uCAAuC;YACvC,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;YAChD,SAAS,KAAK,KAAK,CAAC,eAAe;YAEnC,IAAI,WAAW,GAAG;gBAChB,iBAAiB,sBAAsB;YACzC;QACF;QAEA,iBAAiB,sBAAsB;QAEvC,OAAO;YACL,IAAI,gBAAgB;gBAClB,qBAAqB;YACvB;QACF;IACF,GAAG;QAAC;QAAU;QAAO;KAAS;IAE9B,qBAAO,8OAAC;QAAK,KAAK;kBAAM;;;;;;AAC1B;AAEO,SAAS,MAAM,EAAE,KAAK,EAAc;IACzC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAS;gBACzC,UAAU;gBACV,WAAU;;kCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wBAAE,WAAU;;0CAC1D,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,WAAU;0CAAiC;;;;;;0CAGpE,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAO,WAAU;0CAAoB;;;;;;;;;;;;kCAO3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;gCACpC,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;;0DAG5B,8OAAC;gDAAI,WAAU;;;;;;0DAGf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;oDAAC,SAAQ;oDAAK,WAAU;;wDAChC,KAAK,MAAM;sEACZ,8OAAC;4DAAgB,OAAO,KAAK,KAAK;;;;;;wDACjC,KAAK,MAAM;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAK,WAAU;0DAChC,KAAK,KAAK;;;;;;4CAEZ,KAAK,WAAW,kBACf,8OAAC,sIAAA,CAAA,aAAU;gDAAC,SAAQ;gDAAQ,WAAU;0DACnC,KAAK,WAAW;;;;;;;;;;;;;+BA7BlB,KAAK,EAAE;;;;;;;;;;kCAsClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wBACpC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYxE", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/sections/Skills.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { Typography } from '@/components/ui/Typography';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { ANIMATION_VARIANTS } from '@/lib/constants';\n\ninterface Skill {\n  name: string;\n  level: number; // 1-100\n  category: string;\n}\n\ninterface SkillCategory {\n  name: string;\n  icon: string;\n  skills: Skill[];\n}\n\ninterface SkillsProps {\n  skillCategories: SkillCategory[];\n}\n\nexport function Skills({ skillCategories }: SkillsProps) {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const SkillBar = ({ skill }: { skill: Skill }) => (\n    <div className=\"space-y-2\">\n      <div className=\"flex justify-between items-center\">\n        <Typography variant=\"small\" className=\"font-medium\">\n          {skill.name}\n        </Typography>\n        <Typography variant=\"small\" className=\"text-muted-foreground\">\n          {skill.level}%\n        </Typography>\n      </div>\n      <div className=\"w-full bg-muted rounded-full h-2\">\n        <motion.div\n          className=\"bg-gradient-to-r from-primary to-accent h-2 rounded-full\"\n          initial={{ width: 0 }}\n          whileInView={{ width: `${skill.level}%` }}\n          viewport={{ once: true }}\n          transition={{ duration: 1, delay: 0.2 }}\n        />\n      </div>\n    </div>\n  );\n\n  return (\n    <section className=\"py-20 bg-muted/30\">\n      <div className=\"container mx-auto px-4\">\n        <motion.div\n          initial=\"hidden\"\n          whileInView=\"visible\"\n          viewport={{ once: true, margin: \"-100px\" }}\n          variants={containerVariants}\n          className=\"space-y-12\"\n        >\n          {/* Section Header */}\n          <motion.div variants={ANIMATION_VARIANTS.slideUp} className=\"text-center space-y-4\">\n            <Typography variant=\"h2\" className=\"text-3xl lg:text-4xl font-bold\">\n              Technical Expertise\n            </Typography>\n            <Typography variant=\"lead\" className=\"max-w-2xl mx-auto\">\n              A comprehensive overview of my technical skills, developed through \n              academic research, industry experience, and continuous learning.\n            </Typography>\n          </motion.div>\n\n          {/* Skills Grid */}\n          <motion.div\n            variants={containerVariants}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n          >\n            {skillCategories.map((category, index) => (\n              <motion.div\n                key={category.name}\n                variants={ANIMATION_VARIANTS.slideUp}\n                whileHover={{ y: -5 }}\n                transition={{ duration: 0.2 }}\n              >\n                <Card className=\"h-full hover:shadow-lg transition-all duration-300\">\n                  <CardHeader className=\"text-center space-y-3\">\n                    <div className=\"text-4xl\">{category.icon}</div>\n                    <CardTitle className=\"text-xl\">{category.name}</CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    {category.skills.map((skill) => (\n                      <SkillBar key={skill.name} skill={skill} />\n                    ))}\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Additional Context */}\n          <motion.div\n            variants={ANIMATION_VARIANTS.slideUp}\n            className=\"text-center pt-8\"\n          >\n            <div className=\"max-w-4xl mx-auto p-6 bg-background rounded-lg border\">\n              <Typography variant=\"p\" className=\"text-muted-foreground\">\n                These skills represent years of dedicated learning and practical application. \n                I'm constantly expanding my expertise through research projects, industry collaboration, \n                and staying current with emerging technologies in AI and software development.\n              </Typography>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAuBO,SAAS,OAAO,EAAE,eAAe,EAAe;IACrD,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAoB,iBAC3C,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,WAAU;sCACnC,MAAM,IAAI;;;;;;sCAEb,8OAAC,sIAAA,CAAA,aAAU;4BAAC,SAAQ;4BAAQ,WAAU;;gCACnC,MAAM,KAAK;gCAAC;;;;;;;;;;;;;8BAGjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,aAAa;4BAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;wBAAC;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,UAAU;4BAAG,OAAO;wBAAI;;;;;;;;;;;;;;;;;IAM9C,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAQ;gBACR,aAAY;gBACZ,UAAU;oBAAE,MAAM;oBAAM,QAAQ;gBAAS;gBACzC,UAAU;gBACV,WAAU;;kCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wBAAE,WAAU;;0CAC1D,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAK,WAAU;0CAAiC;;;;;;0CAGpE,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAO,WAAU;0CAAoB;;;;;;;;;;;;kCAO3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;kCAET,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;gCACpC,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;;8DACpB,8OAAC;oDAAI,WAAU;8DAAY,SAAS,IAAI;;;;;;8DACxC,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAW,SAAS,IAAI;;;;;;;;;;;;sDAE/C,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,sBACpB,8OAAC;oDAA0B,OAAO;mDAAnB,MAAM,IAAI;;;;;;;;;;;;;;;;+BAZ1B,SAAS,IAAI;;;;;;;;;;kCAqBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU,uHAAA,CAAA,qBAAkB,CAAC,OAAO;wBACpC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,sIAAA,CAAA,aAAU;gCAAC,SAAQ;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxE", "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/lib/analytics.ts"], "sourcesContent": ["// Advanced Analytics Configuration\nimport { onCLS, onINP, onFCP, onLCP, onTTFB, type Metric } from 'web-vitals'\n\n// Analytics configuration\nexport const ANALYTICS_CONFIG = {\n  GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '',\n  GTAG_ID: process.env.NEXT_PUBLIC_GTAG_ID || '',\n  VERCEL_ANALYTICS: process.env.NEXT_PUBLIC_VERCEL_ANALYTICS_ID || '',\n  ENABLE_DEBUG: process.env.NODE_ENV === 'development',\n} as const\n\n// Custom event types\nexport interface CustomEvent {\n  action: string\n  category: string\n  label?: string\n  value?: number\n  custom_parameters?: Record<string, any>\n}\n\n// Page view tracking\nexport const trackPageView = (url: string, title?: string) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('config', ANALYTICS_CONFIG.GA_MEASUREMENT_ID, {\n      page_title: title,\n      page_location: url,\n    })\n  }\n}\n\n// Custom event tracking\nexport const trackEvent = ({ action, category, label, value, custom_parameters }: CustomEvent) => {\n  if (typeof window !== 'undefined' && window.gtag) {\n    window.gtag('event', action, {\n      event_category: category,\n      event_label: label,\n      value: value,\n      ...custom_parameters,\n    })\n  }\n\n  // Log in development\n  if (ANALYTICS_CONFIG.ENABLE_DEBUG) {\n    console.log('📊 Analytics Event:', { action, category, label, value, custom_parameters })\n  }\n}\n\n// Portfolio-specific event tracking\nexport const portfolioEvents = {\n  // Navigation events\n  navigateToSection: (section: string) => {\n    trackEvent({\n      action: 'navigate_to_section',\n      category: 'navigation',\n      label: section,\n    })\n  },\n\n  // Project interactions\n  viewProject: (projectId: string, projectTitle: string) => {\n    trackEvent({\n      action: 'view_project',\n      category: 'projects',\n      label: projectTitle,\n      custom_parameters: { project_id: projectId },\n    })\n  },\n\n  clickProjectDemo: (projectId: string, projectTitle: string) => {\n    trackEvent({\n      action: 'click_project_demo',\n      category: 'projects',\n      label: projectTitle,\n      custom_parameters: { project_id: projectId },\n    })\n  },\n\n  clickProjectGithub: (projectId: string, projectTitle: string) => {\n    trackEvent({\n      action: 'click_project_github',\n      category: 'projects',\n      label: projectTitle,\n      custom_parameters: { project_id: projectId },\n    })\n  },\n\n  // Research interactions\n  viewPublication: (publicationId: string, publicationTitle: string) => {\n    trackEvent({\n      action: 'view_publication',\n      category: 'research',\n      label: publicationTitle,\n      custom_parameters: { publication_id: publicationId },\n    })\n  },\n\n  downloadPaper: (publicationId: string, publicationTitle: string) => {\n    trackEvent({\n      action: 'download_paper',\n      category: 'research',\n      label: publicationTitle,\n      custom_parameters: { publication_id: publicationId },\n    })\n  },\n\n  // Blog interactions\n  readBlogPost: (postId: string, postTitle: string) => {\n    trackEvent({\n      action: 'read_blog_post',\n      category: 'blog',\n      label: postTitle,\n      custom_parameters: { post_id: postId },\n    })\n  },\n\n  shareBlogPost: (postId: string, postTitle: string, platform: string) => {\n    trackEvent({\n      action: 'share_blog_post',\n      category: 'blog',\n      label: postTitle,\n      custom_parameters: { post_id: postId, platform },\n    })\n  },\n\n  // Contact interactions\n  submitContactForm: (inquiryType: string) => {\n    trackEvent({\n      action: 'submit_contact_form',\n      category: 'contact',\n      label: inquiryType,\n    })\n  },\n\n  downloadResume: () => {\n    trackEvent({\n      action: 'download_resume',\n      category: 'contact',\n      label: 'resume_pdf',\n    })\n  },\n\n  clickSocialLink: (platform: string) => {\n    trackEvent({\n      action: 'click_social_link',\n      category: 'social',\n      label: platform,\n    })\n  },\n\n  // Journey interactions\n  viewJourneyMilestone: (milestoneId: string, milestoneTitle: string) => {\n    trackEvent({\n      action: 'view_journey_milestone',\n      category: 'journey',\n      label: milestoneTitle,\n      custom_parameters: { milestone_id: milestoneId },\n    })\n  },\n\n  // Testimonial interactions\n  viewTestimonial: (testimonialId: string, testimonialAuthor: string) => {\n    trackEvent({\n      action: 'view_testimonial',\n      category: 'testimonials',\n      label: testimonialAuthor,\n      custom_parameters: { testimonial_id: testimonialId },\n    })\n  },\n\n  // Search and filter\n  searchContent: (query: string, category: string) => {\n    trackEvent({\n      action: 'search_content',\n      category: 'search',\n      label: query,\n      custom_parameters: { search_category: category },\n    })\n  },\n\n  filterContent: (filterType: string, filterValue: string) => {\n    trackEvent({\n      action: 'filter_content',\n      category: 'filter',\n      label: filterValue,\n      custom_parameters: { filter_type: filterType },\n    })\n  },\n\n  // User engagement\n  scrollToSection: (section: string, scrollPercentage: number) => {\n    trackEvent({\n      action: 'scroll_to_section',\n      category: 'engagement',\n      label: section,\n      value: scrollPercentage,\n    })\n  },\n\n  timeOnPage: (timeInSeconds: number, page: string) => {\n    trackEvent({\n      action: 'time_on_page',\n      category: 'engagement',\n      label: page,\n      value: timeInSeconds,\n    })\n  },\n}\n\n// Web Vitals tracking\nexport const trackWebVitals = () => {\n  const vitalsUrl = 'https://vitals.vercel-analytics.com/v1/vitals'\n\n  function sendToAnalytics(metric: Metric) {\n    const body = JSON.stringify({\n      dsn: ANALYTICS_CONFIG.VERCEL_ANALYTICS,\n      id: metric.id,\n      page: window.location.pathname,\n      href: window.location.href,\n      event_name: metric.name,\n      value: metric.value.toString(),\n      speed: 'unknown',\n    })\n\n    if (navigator.sendBeacon) {\n      navigator.sendBeacon(vitalsUrl, body)\n    } else {\n      fetch(vitalsUrl, { body, method: 'POST', keepalive: true })\n    }\n\n    // Also send to Google Analytics\n    if (window.gtag) {\n      window.gtag('event', metric.name, {\n        event_category: 'Web Vitals',\n        event_label: metric.id,\n        value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),\n        non_interaction: true,\n      })\n    }\n\n    // Log in development\n    if (ANALYTICS_CONFIG.ENABLE_DEBUG) {\n      console.log('📈 Web Vital:', metric)\n    }\n  }\n\n  // Track all Core Web Vitals\n  onCLS(sendToAnalytics)\n  onINP(sendToAnalytics)\n  onFCP(sendToAnalytics)\n  onLCP(sendToAnalytics)\n  onTTFB(sendToAnalytics)\n}\n\n// Error tracking\nexport const trackError = (error: Error, errorInfo?: any) => {\n  trackEvent({\n    action: 'javascript_error',\n    category: 'error',\n    label: error.message,\n    custom_parameters: {\n      error_stack: error.stack,\n      error_info: errorInfo,\n      user_agent: navigator.userAgent,\n      url: window.location.href,\n    },\n  })\n\n  if (ANALYTICS_CONFIG.ENABLE_DEBUG) {\n    console.error('🚨 Error tracked:', error, errorInfo)\n  }\n}\n\n// User session tracking\nexport const trackUserSession = () => {\n  const sessionStart = Date.now()\n  const sessionId = Math.random().toString(36).substring(2, 15)\n\n  // Track session start\n  trackEvent({\n    action: 'session_start',\n    category: 'session',\n    custom_parameters: {\n      session_id: sessionId,\n      timestamp: sessionStart,\n      user_agent: navigator.userAgent,\n      screen_resolution: `${screen.width}x${screen.height}`,\n      viewport_size: `${window.innerWidth}x${window.innerHeight}`,\n    },\n  })\n\n  // Track session end on page unload\n  window.addEventListener('beforeunload', () => {\n    const sessionEnd = Date.now()\n    const sessionDuration = Math.round((sessionEnd - sessionStart) / 1000)\n\n    trackEvent({\n      action: 'session_end',\n      category: 'session',\n      value: sessionDuration,\n      custom_parameters: {\n        session_id: sessionId,\n        duration_seconds: sessionDuration,\n      },\n    })\n  })\n\n  return sessionId\n}\n\n// A/B Testing support\nexport const trackExperiment = (experimentId: string, variant: string) => {\n  trackEvent({\n    action: 'experiment_view',\n    category: 'experiment',\n    label: experimentId,\n    custom_parameters: {\n      experiment_id: experimentId,\n      variant: variant,\n    },\n  })\n}\n\n// Conversion tracking\nexport const trackConversion = (conversionType: string, value?: number) => {\n  trackEvent({\n    action: 'conversion',\n    category: 'conversion',\n    label: conversionType,\n    value: value,\n  })\n}\n\n// Initialize analytics\nexport const initializeAnalytics = () => {\n  if (typeof window !== 'undefined') {\n    // Track initial page view\n    trackPageView(window.location.href, document.title)\n    \n    // Start user session tracking\n    trackUserSession()\n    \n    // Initialize Web Vitals tracking\n    trackWebVitals()\n    \n    // Track scroll depth\n    let maxScrollDepth = 0\n    window.addEventListener('scroll', () => {\n      const scrollDepth = Math.round(\n        (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100\n      )\n      \n      if (scrollDepth > maxScrollDepth && scrollDepth % 25 === 0) {\n        maxScrollDepth = scrollDepth\n        trackEvent({\n          action: 'scroll_depth',\n          category: 'engagement',\n          label: `${scrollDepth}%`,\n          value: scrollDepth,\n        })\n      }\n    })\n\n    if (ANALYTICS_CONFIG.ENABLE_DEBUG) {\n      console.log('📊 Analytics initialized')\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;;;;;;;;;AACnC;;AAGO,MAAM,mBAAmB;IAC9B,mBAAmB,QAAQ,GAAG,CAAC,6BAA6B,IAAI;IAChE,SAAS,QAAQ,GAAG,CAAC,mBAAmB,IAAI;IAC5C,kBAAkB,QAAQ,GAAG,CAAC,+BAA+B,IAAI;IACjE,cAAc,oDAAyB;AACzC;AAYO,MAAM,gBAAgB,CAAC,KAAa;IACzC,uCAAkD;;IAKlD;AACF;AAGO,MAAM,aAAa,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,iBAAiB,EAAe;IAC3F,uCAAkD;;IAOlD;IAEA,qBAAqB;IACrB,IAAI,iBAAiB,YAAY,EAAE;QACjC,QAAQ,GAAG,CAAC,uBAAuB;YAAE;YAAQ;YAAU;YAAO;YAAO;QAAkB;IACzF;AACF;AAGO,MAAM,kBAAkB;IAC7B,oBAAoB;IACpB,mBAAmB,CAAC;QAClB,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,aAAa,CAAC,WAAmB;QAC/B,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,YAAY;YAAU;QAC7C;IACF;IAEA,kBAAkB,CAAC,WAAmB;QACpC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,YAAY;YAAU;QAC7C;IACF;IAEA,oBAAoB,CAAC,WAAmB;QACtC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,YAAY;YAAU;QAC7C;IACF;IAEA,wBAAwB;IACxB,iBAAiB,CAAC,eAAuB;QACvC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,gBAAgB;YAAc;QACrD;IACF;IAEA,eAAe,CAAC,eAAuB;QACrC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,gBAAgB;YAAc;QACrD;IACF;IAEA,oBAAoB;IACpB,cAAc,CAAC,QAAgB;QAC7B,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,SAAS;YAAO;QACvC;IACF;IAEA,eAAe,CAAC,QAAgB,WAAmB;QACjD,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,SAAS;gBAAQ;YAAS;QACjD;IACF;IAEA,uBAAuB;IACvB,mBAAmB,CAAC;QAClB,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IACF;IAEA,gBAAgB;QACd,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IACF;IAEA,iBAAiB,CAAC;QAChB,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;QACT;IACF;IAEA,uBAAuB;IACvB,sBAAsB,CAAC,aAAqB;QAC1C,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,cAAc;YAAY;QACjD;IACF;IAEA,2BAA2B;IAC3B,iBAAiB,CAAC,eAAuB;QACvC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,gBAAgB;YAAc;QACrD;IACF;IAEA,oBAAoB;IACpB,eAAe,CAAC,OAAe;QAC7B,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,iBAAiB;YAAS;QACjD;IACF;IAEA,eAAe,CAAC,YAAoB;QAClC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBAAE,aAAa;YAAW;QAC/C;IACF;IAEA,kBAAkB;IAClB,iBAAiB,CAAC,SAAiB;QACjC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;QACT;IACF;IAEA,YAAY,CAAC,eAAuB;QAClC,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;QACT;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,YAAY;IAElB,SAAS,gBAAgB,MAAc;QACrC,MAAM,OAAO,KAAK,SAAS,CAAC;YAC1B,KAAK,iBAAiB,gBAAgB;YACtC,IAAI,OAAO,EAAE;YACb,MAAM,OAAO,QAAQ,CAAC,QAAQ;YAC9B,MAAM,OAAO,QAAQ,CAAC,IAAI;YAC1B,YAAY,OAAO,IAAI;YACvB,OAAO,OAAO,KAAK,CAAC,QAAQ;YAC5B,OAAO;QACT;QAEA,IAAI,UAAU,UAAU,EAAE;YACxB,UAAU,UAAU,CAAC,WAAW;QAClC,OAAO;YACL,MAAM,WAAW;gBAAE;gBAAM,QAAQ;gBAAQ,WAAW;YAAK;QAC3D;QAEA,gCAAgC;QAChC,IAAI,OAAO,IAAI,EAAE;YACf,OAAO,IAAI,CAAC,SAAS,OAAO,IAAI,EAAE;gBAChC,gBAAgB;gBAChB,aAAa,OAAO,EAAE;gBACtB,OAAO,KAAK,KAAK,CAAC,OAAO,IAAI,KAAK,QAAQ,OAAO,KAAK,GAAG,OAAO,OAAO,KAAK;gBAC5E,iBAAiB;YACnB;QACF;QAEA,qBAAqB;QACrB,IAAI,iBAAiB,YAAY,EAAE;YACjC,QAAQ,GAAG,CAAC,iBAAiB;QAC/B;IACF;IAEA,4BAA4B;IAC5B,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE;IACN,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE;IACN,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE;IACN,CAAA,GAAA,sJAAA,CAAA,QAAK,AAAD,EAAE;IACN,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE;AACT;AAGO,MAAM,aAAa,CAAC,OAAc;IACvC,WAAW;QACT,QAAQ;QACR,UAAU;QACV,OAAO,MAAM,OAAO;QACpB,mBAAmB;YACjB,aAAa,MAAM,KAAK;YACxB,YAAY;YACZ,YAAY,UAAU,SAAS;YAC/B,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;IACF;IAEA,IAAI,iBAAiB,YAAY,EAAE;QACjC,QAAQ,KAAK,CAAC,qBAAqB,OAAO;IAC5C;AACF;AAGO,MAAM,mBAAmB;IAC9B,MAAM,eAAe,KAAK,GAAG;IAC7B,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAE1D,sBAAsB;IACtB,WAAW;QACT,QAAQ;QACR,UAAU;QACV,mBAAmB;YACjB,YAAY;YACZ,WAAW;YACX,YAAY,UAAU,SAAS;YAC/B,mBAAmB,GAAG,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO,MAAM,EAAE;YACrD,eAAe,GAAG,OAAO,UAAU,CAAC,CAAC,EAAE,OAAO,WAAW,EAAE;QAC7D;IACF;IAEA,mCAAmC;IACnC,OAAO,gBAAgB,CAAC,gBAAgB;QACtC,MAAM,aAAa,KAAK,GAAG;QAC3B,MAAM,kBAAkB,KAAK,KAAK,CAAC,CAAC,aAAa,YAAY,IAAI;QAEjE,WAAW;YACT,QAAQ;YACR,UAAU;YACV,OAAO;YACP,mBAAmB;gBACjB,YAAY;gBACZ,kBAAkB;YACpB;QACF;IACF;IAEA,OAAO;AACT;AAGO,MAAM,kBAAkB,CAAC,cAAsB;IACpD,WAAW;QACT,QAAQ;QACR,UAAU;QACV,OAAO;QACP,mBAAmB;YACjB,eAAe;YACf,SAAS;QACX;IACF;AACF;AAGO,MAAM,kBAAkB,CAAC,gBAAwB;IACtD,WAAW;QACT,QAAQ;QACR,UAAU;QACV,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,sBAAsB;IACjC,uCAAmC;;IA+BnC;AACF", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/pf/src/components/features/resume/ResumeDownload.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Download, FileText, ExternalLink } from 'lucide-react'\nimport { Button } from '@/components/ui/Button'\nimport { portfolioEvents } from '@/lib/analytics'\n\ninterface ResumeDownloadProps {\n  variant?: 'default' | 'outline' | 'ghost'\n  size?: 'sm' | 'default' | 'lg'\n  showIcon?: boolean\n  className?: string\n}\n\nexport function ResumeDownload({ \n  variant = 'default', \n  size = 'default',\n  showIcon = true,\n  className \n}: ResumeDownloadProps) {\n  const [isDownloading, setIsDownloading] = useState(false)\n\n  const handleDownload = async () => {\n    try {\n      setIsDownloading(true)\n      \n      // Track download event\n      portfolioEvents.downloadResume()\n      \n      // Create download link\n      const link = document.createElement('a')\n      link.href = '/resume/Prem_Katuwal_Resume.pdf'\n      link.download = 'Prem_Katuwal_Resume.pdf'\n      link.target = '_blank'\n      \n      // Trigger download\n      document.body.appendChild(link)\n      link.click()\n      document.body.removeChild(link)\n      \n      // Show success message (optional)\n      console.log('Resume download initiated')\n      \n    } catch (error) {\n      console.error('Error downloading resume:', error)\n    } finally {\n      setIsDownloading(false)\n    }\n  }\n\n  return (\n    <Button\n      variant={variant}\n      size={size}\n      onClick={handleDownload}\n      disabled={isDownloading}\n      className={className}\n    >\n      {showIcon && (\n        isDownloading ? (\n          <div className=\"mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent\" />\n        ) : (\n          <Download className=\"mr-2 h-4 w-4\" />\n        )\n      )}\n      {isDownloading ? 'Downloading...' : 'Download Resume'}\n    </Button>\n  )\n}\n\n// Resume preview component\nexport function ResumePreview() {\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handlePreview = () => {\n    setIsLoading(true)\n    \n    // Track preview event\n    portfolioEvents.trackEvent({\n      action: 'preview_resume',\n      category: 'resume',\n      label: 'pdf_preview'\n    })\n    \n    // Open resume in new tab\n    window.open('/resume/Prem_Katuwal_Resume.pdf', '_blank')\n    setIsLoading(false)\n  }\n\n  return (\n    <div className=\"flex items-center space-x-2\">\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={handlePreview}\n        disabled={isLoading}\n      >\n        <FileText className=\"mr-2 h-4 w-4\" />\n        Preview\n      </Button>\n      <ResumeDownload variant=\"default\" size=\"sm\" />\n    </div>\n  )\n}\n\n// Resume section for about page or contact page\nexport function ResumeSection() {\n  return (\n    <div className=\"rounded-lg border bg-card p-6 space-y-4\">\n      <div className=\"flex items-center space-x-3\">\n        <div className=\"flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10\">\n          <FileText className=\"h-6 w-6 text-primary\" />\n        </div>\n        <div>\n          <h3 className=\"font-semibold text-lg\">Resume</h3>\n          <p className=\"text-sm text-muted-foreground\">\n            Download my complete professional resume\n          </p>\n        </div>\n      </div>\n      \n      <div className=\"space-y-3\">\n        <p className=\"text-sm text-muted-foreground\">\n          Get a comprehensive overview of my education at UESTC (ranked 3rd globally in AI), \n          professional software engineering experience, research contributions, and community impact.\n        </p>\n        \n        <div className=\"flex items-center space-x-3\">\n          <ResumeDownload />\n          <Button\n            variant=\"outline\"\n            size=\"default\"\n            onClick={() => window.open('/resume/Prem_Katuwal_Resume.pdf', '_blank')}\n          >\n            <ExternalLink className=\"mr-2 h-4 w-4\" />\n            View Online\n          </Button>\n        </div>\n        \n        <div className=\"text-xs text-muted-foreground\">\n          <p>📄 PDF Format • 📊 Updated December 2024 • 🔒 Professional Version</p>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAcO,SAAS,eAAe,EAC7B,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,WAAW,IAAI,EACf,SAAS,EACW;IACpB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,iBAAiB;QACrB,IAAI;YACF,iBAAiB;YAEjB,uBAAuB;YACvB,uHAAA,CAAA,kBAAe,CAAC,cAAc;YAE9B,uBAAuB;YACvB,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,KAAK,QAAQ,GAAG;YAChB,KAAK,MAAM,GAAG;YAEd,mBAAmB;YACnB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,KAAK;YACV,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,kCAAkC;YAClC,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAS;QACT,MAAM;QACN,SAAS;QACT,UAAU;QACV,WAAW;;YAEV,YAAY,CACX,8BACE,8OAAC;gBAAI,WAAU;;;;;qCAEf,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;oBAExB;YACC,gBAAgB,mBAAmB;;;;;;;AAG1C;AAGO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,gBAAgB;QACpB,aAAa;QAEb,sBAAsB;QACtB,uHAAA,CAAA,kBAAe,CAAC,UAAU,CAAC;YACzB,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QAEA,yBAAyB;QACzB,OAAO,IAAI,CAAC,mCAAmC;QAC/C,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS;gBACT,UAAU;;kCAEV,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;0BAGvC,8OAAC;gBAAe,SAAQ;gBAAU,MAAK;;;;;;;;;;;;AAG7C;AAGO,SAAS;IACd,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;0BAMjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;;;;0CACD,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,mCAAmC;;kDAE9D,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;kCAK7C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;sCAAE;;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}]}