'use client';

import { motion } from 'framer-motion';
import { MessageCircle, Users, Lightbulb, Mic } from 'lucide-react';
import { Typography } from '@/components/ui/Typography';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/Card';
import { ContactForm } from '@/components/features/contact/ContactForm';
import { ContactInfo } from '@/components/features/contact/ContactInfo';
import { ResumeSection } from '@/components/features/resume/ResumeDownload';
import { ANIMATION_VARIANTS } from '@/lib/constants';

export function ContactPageClient() {
  const contactReasons = [
    {
      icon: Users,
      title: 'Research Collaboration',
      description: 'Interested in collaborating on AI research, joint publications, or academic projects.',
      examples: ['Joint research projects', 'Co-authoring papers', 'Data sharing initiatives', 'Cross-institutional studies'],
    },
    {
      icon: Lightbulb,
      title: 'Consulting & Advisory',
      description: 'Looking for technical expertise, strategic advice, or project consultation.',
      examples: ['AI strategy consulting', 'Technical architecture review', 'Product development advice', 'Technology assessment'],
    },
    {
      icon: Mic,
      title: 'Speaking & Presentations',
      description: 'Inviting me to speak at conferences, workshops, or educational events.',
      examples: ['Conference presentations', 'Workshop facilitation', 'Guest lectures', 'Panel discussions'],
    },
    {
      icon: MessageCircle,
      title: 'General Inquiries',
      description: 'Questions about my work, career advice, or just wanting to connect.',
      examples: ['Career guidance', 'Academic advice', 'Technology questions', 'Networking'],
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  return (
    <div className="container mx-auto px-4 py-12 space-y-12">
      {/* Page Header */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center space-y-4"
      >
        <Typography variant="h1" className="text-4xl lg:text-5xl font-bold">
          Let's Connect
        </Typography>
        <Typography variant="lead" className="max-w-3xl mx-auto">
          I'm always excited to discuss new opportunities, share insights, and connect with 
          fellow researchers, developers, and innovators. Whether you have a specific project 
          in mind or just want to chat about technology and research, I'd love to hear from you.
        </Typography>
      </motion.div>

      {/* Contact Reasons */}
      <motion.div
        initial="hidden"
        animate="visible"
        variants={containerVariants}
        className="space-y-8"
      >
        <Typography variant="h2" className="text-2xl font-bold text-center">
          What Can We Discuss?
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {contactReasons.map((reason, index) => (
            <motion.div
              key={reason.title}
              variants={ANIMATION_VARIANTS.slideUp}
              whileHover={{ y: -5 }}
              transition={{ duration: 0.2 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <reason.icon className="h-6 w-6 text-primary" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{reason.title}</CardTitle>
                    </div>
                  </div>
                  <CardDescription className="mt-2">
                    {reason.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <Typography variant="small" className="font-medium text-muted-foreground">
                      Examples:
                    </Typography>
                    <ul className="space-y-1">
                      {reason.examples.map((example, idx) => (
                        <li key={idx} className="text-sm text-muted-foreground flex items-start">
                          <span className="text-primary mr-2">•</span>
                          <span>{example}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </motion.div>

      {/* Main Contact Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        {/* Contact Form */}
        <div className="lg:col-span-2">
          <ContactForm />
        </div>

        {/* Contact Information */}
        <div className="lg:col-span-1 space-y-6">
          <ContactInfo />
          <ResumeSection />
        </div>
      </div>

      {/* FAQ Section */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="space-y-8"
      >
        <Typography variant="h2" className="text-2xl font-bold text-center">
          Frequently Asked Questions
        </Typography>
        <div className="max-w-4xl mx-auto space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">How quickly do you respond to messages?</CardTitle>
            </CardHeader>
            <CardContent>
              <Typography variant="p" className="text-muted-foreground">
                I typically respond to all inquiries within 24-48 hours. For urgent matters, 
                please mention it in your subject line, and I'll prioritize your message.
              </Typography>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">What types of collaborations are you interested in?</CardTitle>
            </CardHeader>
            <CardContent>
              <Typography variant="p" className="text-muted-foreground">
                I'm particularly interested in AI research collaborations, interdisciplinary projects 
                that combine technology with social impact, and opportunities to bridge academic 
                research with practical applications.
              </Typography>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Do you offer mentoring or career advice?</CardTitle>
            </CardHeader>
            <CardContent>
              <Typography variant="p" className="text-muted-foreground">
                Yes! I enjoy mentoring students and early-career professionals, especially those 
                interested in AI research, software engineering, or transitioning between industry 
                and academia. Feel free to reach out with specific questions.
              </Typography>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Are you available for speaking engagements?</CardTitle>
            </CardHeader>
            <CardContent>
              <Typography variant="p" className="text-muted-foreground">
                I'm open to speaking at conferences, workshops, and educational events, particularly 
                on topics related to AI research, software engineering best practices, and career 
                transitions. Please provide details about your event and timeline.
              </Typography>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial="hidden"
        whileInView="visible"
        viewport={{ once: true }}
        variants={ANIMATION_VARIANTS.slideUp}
        className="text-center pt-8"
      >
        <div className="max-w-2xl mx-auto p-8 bg-gradient-to-br from-primary/5 to-accent/5 rounded-lg border">
          <Typography variant="h3" className="mb-4">
            Ready to Start a Conversation?
          </Typography>
          <Typography variant="p" className="text-muted-foreground mb-6">
            Whether you have a specific project in mind, want to explore collaboration opportunities, 
            or simply want to connect and share ideas, I'm here to listen and engage. 
            Let's create something meaningful together.
          </Typography>
          <Typography variant="small" className="text-muted-foreground">
            I look forward to hearing from you and learning about your projects, ideas, and goals.
          </Typography>
        </div>
      </motion.div>
    </div>
  );
}
